"""
WhatsApp Sender Backend Script
Aggregates all WhatsApp message sending functionality.
"""
import os
import json
import logging
import requests
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class WhatsAppSender:
    """Base class for WhatsApp message sending"""

    def __init__(self, api_key: str):
        """Initialize the WhatsApp sender with API key"""
        self.api_key = api_key
        self.logger = self._setup_logger()
        self.sender_name = ""
        self.phone_number = ""

    def _setup_logger(self):
        """Set up logger for the sender"""
        logger = logging.getLogger("openengage.whatsapp.sender")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def set_sender_details(self, sender_name: str, phone_number: str):
        """Set sender details"""
        # Clean phone number - remove any non-digit characters except +
        clean_phone = ''.join(c for c in phone_number if c.isdigit() or c == '+')
        
        # Ensure phone number starts with country code
        if not clean_phone.startswith('+'):
            if clean_phone.startswith('91'):  # India
                clean_phone = '+' + clean_phone
            elif len(clean_phone) == 10:  # Assume Indian number without country code
                clean_phone = '+91' + clean_phone
            else:
                clean_phone = '+' + clean_phone

        self.sender_name = sender_name
        self.phone_number = clean_phone

        self.logger.info(f"Original sender phone: {phone_number}, Cleaned sender phone: {self.phone_number}")
        self.logger.info(f"Using sender: {self.sender_name} <{self.phone_number}>")

    def send_messages(self, message_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send WhatsApp messages using the configured provider.

        Args:
            message_data: DataFrame containing message data

        Returns:
            Dict with results of the send operation
        """
        raise NotImplementedError("Subclasses must implement send_messages")

    def validate_api_key(self):
        """Validate the API key"""
        raise NotImplementedError("Subclasses must implement validate_api_key")


class GupshupSender(WhatsAppSender):
    """Gupshup implementation of WhatsApp sender"""

    def __init__(self, api_key: str):
        """Initialize the Gupshup sender with API key"""
        super().__init__(api_key)

        # Get sender details from environment variables
        sender_name = os.getenv("WHATSAPP_SENDER_NAME", "OpenEngage")
        phone_number = os.getenv("WHATSAPP_PHONE_NUMBER", "")

        # Store the values
        self.sender_name = sender_name
        self.phone_number = phone_number

        # Log sender details
        self.logger.info(f"Using sender: {self.sender_name} <{self.phone_number}>")

        # Validate the API key
        self.validate_api_key()

    def validate_api_key(self):
        """Validate the Gupshup API key"""
        if not self.api_key:
            raise ValueError("Gupshup API key is required")

    def send_messages(self, message_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Send WhatsApp messages using Gupshup.

        Args:
            message_data: DataFrame containing message data with columns:
                - phone_number: Recipient's phone number
                - template_id: ID of the template to use
                - params: List of parameters to substitute in the template

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Preparing to send {len(message_data)} WhatsApp messages via Gupshup")

        # Add result columns
        message_data["Status"] = ""
        message_data["Message_ID"] = ""
        message_data["Time_Send"] = ""

        # Process messages
        results = {
            "total_sent": 0,
            "total_accepted": 0,
            "total_rejected": 0,
            "errors": []
        }

        # Gupshup API endpoint
        url = "https://api.gupshup.io/wa/api/v1/template/msg"

        # Headers for the API request
        headers = {
            "accept": "application/json",
            "apikey": self.api_key,
            "content-type": "application/x-www-form-urlencoded"
        }

        # Base payload
        payload = {
            "source": self.phone_number,
            "src.name": self.sender_name,
            "destination": "",
            "channel": "whatsapp",
            "template": ""
        }

        # Process each message
        for idx, row in message_data.iterrows():
            try:
                # Get message details
                phone_number = str(row['phone_number'])
                template_id = str(row['template_id'])
                params = row.get('params', [])

                # Ensure params is a list
                if isinstance(params, str):
                    params = [params]
                elif not isinstance(params, list):
                    params = []

                # Create template JSON
                template_json = {
                    "id": template_id,
                    "params": params
                }

                # Update payload for this message
                payload["destination"] = phone_number
                payload["template"] = json.dumps(template_json)

                # Send the message
                response = requests.post(url, headers=headers, data=payload)

                # Process response
                if response.status_code == 202:
                    # Success
                    response_data = response.json()
                    message_id = response_data.get("messageId", "")
                    
                    message_data.at[idx, "Status"] = "Accepted"
                    message_data.at[idx, "Message_ID"] = message_id
                    message_data.at[idx, "Time_Send"] = datetime.now().isoformat()
                    
                    results["total_accepted"] += 1
                    self.logger.info(f"Message sent successfully to {phone_number}, ID: {message_id}")
                else:
                    # Error
                    error_msg = f"Failed to send to {phone_number}: {response.status_code} - {response.text}"
                    message_data.at[idx, "Status"] = "Rejected"
                    message_data.at[idx, "Message_ID"] = ""
                    message_data.at[idx, "Time_Send"] = datetime.now().isoformat()
                    
                    results["total_rejected"] += 1
                    results["errors"].append(error_msg)
                    self.logger.error(error_msg)

                results["total_sent"] += 1

            except Exception as e:
                error_msg = f"Error sending message to {row.get('phone_number', 'unknown')}: {str(e)}"
                message_data.at[idx, "Status"] = "Error"
                message_data.at[idx, "Message_ID"] = ""
                message_data.at[idx, "Time_Send"] = datetime.now().isoformat()
                
                results["errors"].append(error_msg)
                self.logger.error(error_msg)

        return results

    def send_test_message(self, phone_number: str, template_id: str, params: List[str]) -> Dict[str, Any]:
        """
        Send a test WhatsApp message.

        Args:
            phone_number: Recipient's phone number
            template_id: ID of the template to use
            params: List of parameters to substitute in the template

        Returns:
            Dict with results of the send operation
        """
        self.logger.info(f"Sending test WhatsApp message to {phone_number}")

        # Create a DataFrame with a single row
        df = pd.DataFrame({
            "phone_number": [phone_number],
            "template_id": [template_id],
            "params": [params]
        })

        # Send the message
        return self.send_messages(df)


def create_whatsapp_sender(provider: str, api_key: str) -> WhatsAppSender:
    """
    Create a WhatsApp sender instance based on the provider.

    Args:
        provider: Name of the WhatsApp service provider
        api_key: API key for the provider

    Returns:
        WhatsAppSender instance
    """
    if provider.lower() == "gupshup":
        return GupshupSender(api_key)
    elif provider.lower() == "twilio":
        # TODO: Implement TwilioSender
        raise NotImplementedError("Twilio WhatsApp sender not implemented yet")
    elif provider.lower() == "messagebird":
        # TODO: Implement MessageBirdSender
        raise NotImplementedError("MessageBird WhatsApp sender not implemented yet")
    else:
        raise ValueError(f"Unsupported WhatsApp provider: {provider}")


def load_sending_configuration() -> Dict[str, Any]:
    """
    Load sending configuration from file.
    
    Returns:
        Dict containing sending configuration
    """
    config_file = 'data/sending_configuration.json'
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return {}
    
    return {}


def get_api_keys() -> Dict[str, str]:
    """
    Get API keys from configuration.
    
    Returns:
        Dict containing API keys for different providers
    """
    config = load_sending_configuration()
    
    # Extract API keys from configuration
    api_keys = {}
    
    # Get WhatsApp API keys
    whatsapp_config = config.get("whatsapp", {})
    for provider in ["gupshup", "twilio", "messagebird"]:
        provider_config = whatsapp_config.get(provider, {})
        api_keys[provider] = provider_config.get("api_key", "")
    
    # Fallback to environment variables
    if not api_keys.get("gupshup"):
        api_keys["gupshup"] = os.getenv("GUPSHUP_API_KEY", "")
    
    return api_keys


def send_test_whatsapp_message(recipient_phone: str, selected_template_id: str, variable_values: List[str], 
                              selected_esp: str = "Gupshup") -> Dict[str, Any]:
    """
    Send a test WhatsApp message using the specified template and parameters.
    
    Args:
        recipient_phone: Recipient's phone number with country code
        selected_template_id: ID of the template to use
        variable_values: List of values for template variables
        selected_esp: WhatsApp service provider (default: Gupshup)
    
    Returns:
        Dict with results of the send operation
    """
    try:
        # Get API keys
        api_keys = get_api_keys()
        
        # Get current sending details
        config = load_sending_configuration()
        current_details = config.get("current_details", {})

        # Configure WhatsApp sender
        if selected_esp.lower() == "gupshup":
            api_key = api_keys.get("gupshup", "")
            sender = create_whatsapp_sender("gupshup", api_key)
        elif selected_esp.lower() == "twilio":
            api_key = api_keys.get("twilio", "")
            sender = create_whatsapp_sender("twilio", api_key)
        elif selected_esp.lower() == "messagebird":
            api_key = api_keys.get("messagebird", "")
            sender = create_whatsapp_sender("messagebird", api_key)
        else:
            return {
                "success": False,
                "error": f"Unsupported WhatsApp provider: {selected_esp}"
            }

        # Set sender details for WhatsApp
        if current_details:
            # For WhatsApp, we're using the sender_email field to store the phone number
            sender.set_sender_details(
                current_details.get("sender_name", ""),
                current_details.get("sender_email", "")  # Using email field for phone number
            )

        # Send test message
        result = sender.send_test_message(
            phone_number=recipient_phone,
            template_id=selected_template_id,
            params=variable_values
        )

        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def send_whatsapp_campaign(campaign_data: pd.DataFrame, provider: str = "Gupshup") -> Dict[str, Any]:
    """
    Send a WhatsApp campaign to multiple recipients.
    
    Args:
        campaign_data: DataFrame containing campaign data with columns:
            - phone_number: Recipient's phone number
            - template_id: Template ID to use
            - param_1: First template parameter
            - param_2: Second template parameter
        provider: WhatsApp service provider
    
    Returns:
        Dict with campaign results
    """
    try:
        # Get API keys
        api_keys = get_api_keys()
        
        # Create sender
        api_key = api_keys.get(provider.lower(), "")
        if not api_key:
            return {
                "success": False,
                "error": f"No API key found for provider: {provider}"
            }
        
        sender = create_whatsapp_sender(provider, api_key)
        
        # Prepare message data
        message_data = campaign_data.copy()
        
        # Convert param_1 and param_2 to params list
        message_data['params'] = message_data.apply(
            lambda row: [str(row.get('param_1', '')), str(row.get('param_2', ''))], 
            axis=1
        )
        
        # Send messages
        results = sender.send_messages(message_data)
        
        return {
            "success": True,
            "results": results,
            "campaign_data": message_data
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def send_whatsapp_messages_one_by_one(campaign_df: pd.DataFrame, progress_callback=None) -> Dict[str, Any]:
    """
    Send WhatsApp messages one by one with progress tracking.
    
    Args:
        campaign_df: DataFrame containing campaign data
        progress_callback: Optional callback function for progress updates
    
    Returns:
        Dict with sending results
    """
    total_messages = len(campaign_df)
    results = {
        "total_sent": 0,
        "total_accepted": 0,
        "total_rejected": 0,
        "errors": []
    }
    
    # Process each message one by one
    for idx, row in campaign_df.iterrows():
        # Update progress
        if progress_callback:
            progress_callback(idx, total_messages, f"Sending message to {row['phone_number']} ({idx+1}/{total_messages})...")

        try:
            # Get message parameters
            phone_number = str(row['phone_number'])
            template_id = str(row['Template_ID'])
            param_1 = str(row['param_1']) if 'param_1' in row else ""
            param_2 = str(row['param_2']) if 'param_2' in row else ""

            # Create variable values list
            variable_values = [param_1, param_2]

            # Send the message using gupshup_whatsapp_sender
            message_result = send_test_whatsapp_message(
                recipient_phone=phone_number,
                selected_template_id=template_id,
                variable_values=variable_values,
                selected_esp="Gupshup"  # Default to Gupshup
            )

            # Update results
            results["total_sent"] += 1
            
            if message_result.get("success", False):
                results["total_accepted"] += 1
                campaign_df.at[idx, 'Status'] = 'Sent'
            else:
                results["total_rejected"] += 1
                results["errors"].append(f"Failed to send to {phone_number}: {message_result.get('error', 'Unknown error')}")
                campaign_df.at[idx, 'Status'] = 'Failed'

        except Exception as e:
            results["errors"].append(f"Error sending to {row.get('phone_number', 'unknown')}: {str(e)}")
            campaign_df.at[idx, 'Status'] = 'Error'

    # Final progress update
    if progress_callback:
        progress_callback(total_messages, total_messages, f"Campaign completed. Sent {results['total_accepted']}/{total_messages} messages successfully.")

    return results


# Additional utility functions for WhatsApp campaign management

def load_whatsapp_templates() -> Dict[str, Any]:
    """
    Load WhatsApp templates from local storage.

    Returns:
        Dict containing templates data
    """
    templates_file = 'data/whatsapp_templates.json'

    if os.path.exists(templates_file):
        try:
            with open(templates_file, 'r') as f:
                return json.load(f)
        except json.JSONDecodeError:
            return {"templates": [], "product_templates": {}}

    return {"templates": [], "product_templates": {}}


def get_template_for_product(product_name: str) -> Optional[str]:
    """
    Get the template ID mapped to a specific product.

    Args:
        product_name: Name of the product

    Returns:
        Template ID or None if not found
    """
    templates_data = load_whatsapp_templates()
    return templates_data.get("product_templates", {}).get(product_name)


def prepare_whatsapp_campaign_data(user_data: pd.DataFrame, product_name: str,
                                  template_id: str = None) -> pd.DataFrame:
    """
    Prepare campaign data for WhatsApp sending.

    Args:
        user_data: DataFrame containing user data
        product_name: Name of the product
        template_id: Optional template ID (will auto-select if not provided)

    Returns:
        DataFrame prepared for WhatsApp campaign
    """
    campaign_df = user_data.copy()

    # Get template ID if not provided
    if not template_id:
        template_id = get_template_for_product(product_name)
        if not template_id:
            # Use a default template or raise an error
            templates_data = load_whatsapp_templates()
            all_templates = templates_data.get("templates", [])
            if all_templates:
                template_id = all_templates[0].get("template_id", "")
            else:
                raise ValueError(f"No template found for product: {product_name}")

    # Add template ID to campaign data
    campaign_df['Template_ID'] = template_id

    # Ensure required columns exist
    if 'phone_number' not in campaign_df.columns:
        raise ValueError("Campaign data must contain 'phone_number' column")

    # Set default parameters if not present
    if 'param_1' not in campaign_df.columns:
        campaign_df['param_1'] = campaign_df.get('first_name', 'Customer')

    if 'param_2' not in campaign_df.columns:
        campaign_df['param_2'] = f"Check out our {product_name}!"

    return campaign_df


def validate_phone_numbers(phone_numbers: List[str]) -> Dict[str, Any]:
    """
    Validate a list of phone numbers for WhatsApp sending.

    Args:
        phone_numbers: List of phone numbers to validate

    Returns:
        Dict containing validation results
    """
    valid_numbers = []
    invalid_numbers = []

    for phone in phone_numbers:
        # Clean phone number
        clean_phone = ''.join(c for c in str(phone) if c.isdigit() or c == '+')

        # Basic validation
        if len(clean_phone) >= 10:  # Minimum length check
            if not clean_phone.startswith('+'):
                if clean_phone.startswith('91') and len(clean_phone) == 12:
                    clean_phone = '+' + clean_phone
                elif len(clean_phone) == 10:
                    clean_phone = '+91' + clean_phone
                else:
                    clean_phone = '+' + clean_phone

            valid_numbers.append(clean_phone)
        else:
            invalid_numbers.append(phone)

    return {
        "valid_numbers": valid_numbers,
        "invalid_numbers": invalid_numbers,
        "valid_count": len(valid_numbers),
        "invalid_count": len(invalid_numbers)
    }


def generate_whatsapp_campaign_report(results: Dict[str, Any], campaign_df: pd.DataFrame) -> Dict[str, Any]:
    """
    Generate a comprehensive campaign report.

    Args:
        results: Results from send operation
        campaign_df: Campaign DataFrame with status updates

    Returns:
        Dict containing campaign report
    """
    total_messages = len(campaign_df)

    # Count statuses
    status_counts = campaign_df['Status'].value_counts().to_dict() if 'Status' in campaign_df.columns else {}

    # Calculate rates
    success_rate = (results.get("total_accepted", 0) / total_messages * 100) if total_messages > 0 else 0
    failure_rate = (results.get("total_rejected", 0) / total_messages * 100) if total_messages > 0 else 0

    report = {
        "campaign_summary": {
            "total_messages": total_messages,
            "messages_sent": results.get("total_sent", 0),
            "messages_accepted": results.get("total_accepted", 0),
            "messages_rejected": results.get("total_rejected", 0),
            "success_rate": round(success_rate, 2),
            "failure_rate": round(failure_rate, 2)
        },
        "status_breakdown": status_counts,
        "errors": results.get("errors", []),
        "timestamp": datetime.now().isoformat()
    }

    return report


def save_campaign_results(campaign_df: pd.DataFrame, results: Dict[str, Any],
                         campaign_name: str = None) -> str:
    """
    Save campaign results to file.

    Args:
        campaign_df: Campaign DataFrame with results
        results: Campaign results dictionary
        campaign_name: Optional campaign name

    Returns:
        Path to saved file
    """
    # Create results directory if it doesn't exist
    results_dir = "data/whatsapp_campaign_results"
    os.makedirs(results_dir, exist_ok=True)

    # Generate filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if campaign_name:
        filename = f"{campaign_name}_{timestamp}.csv"
    else:
        filename = f"whatsapp_campaign_{timestamp}.csv"

    filepath = os.path.join(results_dir, filename)

    # Save campaign data
    campaign_df.to_csv(filepath, index=False)

    # Save summary report
    report = generate_whatsapp_campaign_report(results, campaign_df)
    report_filename = filepath.replace('.csv', '_report.json')

    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)

    return filepath


def execute_whatsapp_campaign(user_data: pd.DataFrame, product_name: str,
                             template_id: str = None, provider: str = "Gupshup",
                             progress_callback=None, save_results: bool = True) -> Dict[str, Any]:
    """
    Execute a complete WhatsApp campaign.

    Args:
        user_data: DataFrame containing user data
        product_name: Name of the product
        template_id: Optional template ID
        provider: WhatsApp service provider
        progress_callback: Optional progress callback function
        save_results: Whether to save results to file

    Returns:
        Dict containing campaign execution results
    """
    try:
        # Prepare campaign data
        campaign_df = prepare_whatsapp_campaign_data(user_data, product_name, template_id)

        # Validate phone numbers
        phone_validation = validate_phone_numbers(campaign_df['phone_number'].tolist())

        if phone_validation["invalid_count"] > 0:
            print(f"Warning: {phone_validation['invalid_count']} invalid phone numbers found")

        # Send campaign
        if progress_callback:
            results = send_whatsapp_messages_one_by_one(campaign_df, progress_callback)
        else:
            results = send_whatsapp_campaign(campaign_df, provider)

        # Generate report
        report = generate_whatsapp_campaign_report(results, campaign_df)

        # Save results if requested
        saved_file = None
        if save_results:
            saved_file = save_campaign_results(campaign_df, results, product_name)

        return {
            "success": True,
            "campaign_results": results,
            "campaign_report": report,
            "phone_validation": phone_validation,
            "saved_file": saved_file,
            "campaign_data": campaign_df
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }
