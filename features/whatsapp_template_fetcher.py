"""
WhatsApp Template Fetcher Backend Script
Aggregates all WhatsApp template fetching and management functionality.
"""
import os
import json
import logging
import requests
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class WhatsAppTemplateManager:
    """Manager for WhatsApp templates"""

    def __init__(self, templates_file: str = 'data/whatsapp_templates.json'):
        """
        Initialize the WhatsApp template manager.

        Args:
            templates_file: Path to the templates JSON file
        """
        self.templates_file = templates_file
        self.logger = self._setup_logger()
        self.templates = self._load_templates()

    def _setup_logger(self):
        """Set up logger for the template manager"""
        logger = logging.getLogger("openengage.whatsapp.template_manager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _load_templates(self) -> Dict[str, Any]:
        """
        Load templates from the JSON file.

        Returns:
            Dict containing templates data
        """
        if os.path.exists(self.templates_file):
            try:
                with open(self.templates_file, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                self.logger.warning(f"Error parsing {self.templates_file}. Using empty templates.")
                return {"templates": [], "product_templates": {}}
        else:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.templates_file), exist_ok=True)
            # Return empty templates structure
            return {"templates": [], "product_templates": {}}

    def _save_templates(self) -> bool:
        """
        Save templates to the JSON file.

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.templates_file, 'w') as f:
                json.dump(self.templates, f, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"Error saving templates: {str(e)}")
            return False

    def add_template(self, template_id: str, template_name: str, description: str,
                     variable_count: int = 2, example_text: str = None, button_data: dict = None) -> bool:
        """
        Add a new template.

        Args:
            template_id: ID of the template from Gupshup
            template_name: Name of the template
            description: Description of the template
            variable_count: Number of variables in the template (default: 2)
            example_text: Example text with variables (default: standard template)
            button_data: Optional button configuration data

        Returns:
            True if successful, False otherwise
        """
        # Always use 2 variables ({{1}} and {{2}})
        variable_count = 2

        # Use default example text if none provided
        if not example_text:
            example_text = f"Hi {{{{1}}}},\n\n{{{{2}}}}\n\nBest regards,\nAnalytics Vidhya"

        # Check if template already exists
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                # Update existing template
                template.update({
                    "template_name": template_name,
                    "description": description,
                    "variable_count": variable_count,
                    "example_text": example_text
                })

                # Add button data if provided
                if button_data:
                    template["button_data"] = button_data

                return self._save_templates()

        # Add new template
        if "templates" not in self.templates:
            self.templates["templates"] = []

        # Create new template data
        template_data = {
            "template_id": template_id,
            "template_name": template_name,
            "description": description,
            "variable_count": variable_count,
            "example_text": example_text,
            "created_at": str(datetime.now())
        }

        # Add button data if provided
        if button_data:
            template_data["button_data"] = button_data

        self.templates["templates"].append(template_data)

        return self._save_templates()

    def get_templates(self) -> List[Dict[str, Any]]:
        """
        Get all templates.

        Returns:
            List of template dictionaries
        """
        return self.templates.get("templates", [])

    def get_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific template by ID.

        Args:
            template_id: ID of the template

        Returns:
            Template dictionary or None if not found
        """
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                return template
        return None

    def map_product_template(self, product_name: str, template_id: str) -> bool:
        """
        Map a product to a template. Each product can only have one template.
        If a product already has a template, it will be updated.

        Args:
            product_name: Name of the product
            template_id: ID of the template

        Returns:
            True if successful, False otherwise
        """
        # Check if template exists
        template_exists = False
        for template in self.templates.get("templates", []):
            if template.get("template_id") == template_id:
                template_exists = True
                break

        if not template_exists:
            self.logger.error(f"Template {template_id} does not exist")
            return False

        # Add or update product-template mapping
        if "product_templates" not in self.templates:
            self.templates["product_templates"] = {}

        self.templates["product_templates"][product_name] = template_id

        return self._save_templates()

    def get_product_template(self, product_name: str) -> Optional[str]:
        """
        Get the template ID for a product.

        Args:
            product_name: Name of the product

        Returns:
            Template ID or None if not mapped
        """
        return self.templates.get("product_templates", {}).get(product_name)

    def get_template_id_for_product(self, product_name: str) -> str:
        """
        Get the template ID for a product. If multiple templates are mapped to the product,
        returns the primary template ID. If no template is mapped, randomly selects a template.

        Args:
            product_name: Name of the product

        Returns:
            Template ID or a randomly selected template ID if not mapped
        """
        # First try to get from product_templates (primary mapping)
        template_id = self.templates.get("product_templates", {}).get(product_name)
        if template_id:
            return template_id

        # If not found, try to get from product_template_mappings
        template_ids = self.templates.get("product_template_mappings", {}).get(product_name, [])
        if template_ids:
            return template_ids[0]  # Return the first template ID

        # If still not found, randomly select a template from all available templates
        all_templates = self.get_templates()
        if all_templates:
            import random
            random_template = random.choice(all_templates)
            template_id = random_template.get("template_id", "")
            self.logger.info(f"No template mapped for product '{product_name}'. Randomly selected template: {template_id}")
            return template_id

        # If no templates available at all, return empty string
        return ""

    def get_product_templates(self) -> Dict[str, str]:
        """
        Get all product-template mappings (single template per product).

        Returns:
            Dict of product names to template IDs
        """
        return self.templates.get("product_templates", {})

    def delete_template(self, template_id: str) -> bool:
        """
        Delete a template.

        Args:
            template_id: ID of the template

        Returns:
            True if successful, False otherwise
        """
        # Remove template from templates list
        templates = self.templates.get("templates", [])
        self.templates["templates"] = [t for t in templates if t.get("template_id") != template_id]

        # Remove template from product mappings
        product_templates = self.templates.get("product_templates", {})
        for product, tid in list(product_templates.items()):
            if tid == template_id:
                del product_templates[product]

        return self._save_templates()


def fetch_gupshup_templates(api_key: str = None, app_id: str = None) -> Dict[str, Any]:
    """
    Fetch templates from Gupshup API.
    
    Args:
        api_key: Gupshup API key (optional, will use env var if not provided)
        app_id: Gupshup app ID (optional, will use env var if not provided)
    
    Returns:
        Dict containing templates data or error information
    """
    # Use provided values or fall back to environment variables
    if not api_key:
        api_key = os.getenv("GUPSHUP_API_KEY", "vrn3dzx6eqoejckvtnijjo2iddlfvxhj")
    
    if not app_id:
        app_id = os.getenv("GUPSHUP_APP_ID", "08a81bd9-3865-4db3-8eb2-97d1cf59d153")
    
    url = f"https://api.gupshup.io/wa/app/{app_id}/template"
    headers = {
        "accept": "application/json",
        "apikey": api_key
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = json.loads(response.text)
            
            # Check if 'templates' key exists in the response
            if 'templates' in data and isinstance(data['templates'], list):
                templates_list = data['templates']
                
                # Convert to DataFrame for easier processing
                df = pd.DataFrame(templates_list)
                
                return {
                    "success": True,
                    "templates": templates_list,
                    "templates_df": df,
                    "count": len(templates_list)
                }
            else:
                return {
                    "success": False,
                    "error": "No templates found in response",
                    "response": data
                }
        else:
            return {
                "success": False,
                "error": f"API request failed with status {response.status_code}",
                "response": response.text
            }
            
    except requests.exceptions.Timeout:
        return {
            "success": False,
            "error": "Request timed out"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


def get_template_details(product_name: Optional[str] = None, template_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get template details either by product name or template ID.
    
    Args:
        product_name: Name of the product to get template for
        template_id: ID of the template to get details for
        
    Returns:
        Template details or None if not found
    """
    try:
        template_manager = WhatsAppTemplateManager()
        
        if template_id:
            return template_manager.get_template(template_id)
        elif product_name:
            template_id = template_manager.get_product_template(product_name)
            if template_id:
                return template_manager.get_template(template_id)
        
        return None
    except Exception as e:
        print(f"Error getting template details: {str(e)}")
        return None


# Main functions for external use
def fetch_and_store_templates(api_key: str = None, app_id: str = None) -> Dict[str, Any]:
    """
    Fetch templates from Gupshup and store them locally.
    
    Args:
        api_key: Gupshup API key
        app_id: Gupshup app ID
    
    Returns:
        Dict with operation results
    """
    # Fetch templates from API
    fetch_result = fetch_gupshup_templates(api_key, app_id)
    
    if not fetch_result["success"]:
        return fetch_result
    
    # Store templates locally
    template_manager = WhatsAppTemplateManager()
    stored_count = 0
    
    for template in fetch_result["templates"]:
        template_id = template.get("id", "")
        template_name = template.get("elementName", "")
        
        if template_id and template_name:
            success = template_manager.add_template(
                template_id=template_id,
                template_name=template_name,
                description=f"Template fetched from Gupshup: {template_name}"
            )
            if success:
                stored_count += 1
    
    return {
        "success": True,
        "fetched_count": fetch_result["count"],
        "stored_count": stored_count,
        "templates": fetch_result["templates"]
    }


def get_all_templates() -> Dict[str, Any]:
    """
    Get all stored templates.
    
    Returns:
        Dict containing all templates and mappings
    """
    template_manager = WhatsAppTemplateManager()
    
    return {
        "templates": template_manager.get_templates(),
        "product_mappings": template_manager.get_product_templates(),
        "count": len(template_manager.get_templates())
    }


def create_product_template_mapping(product_name: str, template_id: str) -> Dict[str, Any]:
    """
    Create a mapping between a product and a template.
    
    Args:
        product_name: Name of the product
        template_id: ID of the template
    
    Returns:
        Dict with operation results
    """
    template_manager = WhatsAppTemplateManager()
    
    success = template_manager.map_product_template(product_name, template_id)
    
    return {
        "success": success,
        "product_name": product_name,
        "template_id": template_id,
        "message": f"Successfully mapped {product_name} to template {template_id}" if success else "Failed to create mapping"
    }
