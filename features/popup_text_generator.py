"""
Popup Text Generator Backend Script
Aggregates all popup text generation and content creation functionality.
"""
import os
import json
import logging
import pandas as pd
import yaml
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from pathlib import Path
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import openai
from openai import OpenAI

class PopupTextGenerator:
    """Generator for popup text content"""

    def __init__(self, api_key: str = None):
        """
        Initialize the popup text generator.

        Args:
            api_key: OpenAI API key (optional, will use env var if not provided)
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.client = OpenAI(api_key=self.api_key) if self.api_key else None
        self.logger = self._setup_logger()
        self.default_prompt = self._get_default_prompt()

    def _setup_logger(self):
        """Set up logger for the text generator"""
        logger = logging.getLogger("openengage.popup.text_generator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _get_default_prompt(self) -> str:
        """
        Get default prompt template for popup generation.

        Returns:
            Default prompt template string
        """
        return """Generate personalized popup content for a website visitor based on their data and behavior.

        User Details:
        First Name: {first_name}
        User Behavior: {user_behavior}
        Target Product: {target_product}
        User Stage: {user_stage}

        Requirements:
        - Create a popup that feels tailored to this specific user's needs and interests
        - The content should be engaging, concise, and drive conversion
        - Avoid generic marketing language
        - If behavior data is not available, focus on the target product and user stage

        Return a JSON object with these fields:
        - popup_title: A short, attention-grabbing title (max 8 words)
        - popup_text: Compelling text (5-10 words) about why they should check out this product
        - button_text: Call-to-action text for the button (1-3 words)"""

    def load_prompt_template(self) -> str:
        """
        Load prompt template from prompts.yml file.

        Returns:
            Prompt template string
        """
        try:
            prompts_path = Path(__file__).parent.parent.parent.parent / 'config' / 'prompts.yml'
            if prompts_path.exists():
                with open(prompts_path, 'r') as f:
                    prompts = yaml.safe_load(f)
                    template = prompts.get('popup_generation', {}).get('template', '')
                    if template:
                        self.logger.info("Loaded prompt template from prompts.yml")
                        return template
        except Exception as e:
            self.logger.warning(f"Error loading prompts.yml: {str(e)}")
        
        self.logger.info("Using default prompt template")
        return self.default_prompt

    def generate_popup_content(self, first_name: str, user_behavior: str, target_product: str, 
                              user_stage: str, user_email: str = "", redirect_url: str = "") -> Dict[str, Any]:
        """
        Generate personalized popup content for a single user.

        Args:
            first_name: User's first name
            user_behavior: User's behavior data
            target_product: Target product for the user
            user_stage: User's stage in the journey
            user_email: User's email address
            redirect_url: Redirect URL for the popup

        Returns:
            Dict containing generated popup content
        """
        try:
            if not self.client:
                raise ValueError("OpenAI API key not provided")

            # Load prompt template
            prompt_template = self.load_prompt_template()

            # Format the prompt with user data
            formatted_prompt = prompt_template.format(
                first_name=first_name,
                user_behavior=user_behavior,
                target_product=target_product,
                user_stage=user_stage
            )

            # Generate content using OpenAI
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a marketing content generator. Generate personalized popup content in JSON format."},
                    {"role": "user", "content": formatted_prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )

            # Extract content from response
            content = response.choices[0].message.content
            
            # Parse JSON response
            try:
                response_json = json.loads(content)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                response_json = {
                    "popup_title": f"Discover {target_product}",
                    "popup_text": "Enhance your skills today!",
                    "button_text": "Learn More"
                }

            # Extract popup content
            popup_title = response_json.get("popup_title", f"Discover {target_product}")
            popup_text = response_json.get("popup_text", "Enhance your skills today!")
            button_text = response_json.get("button_text", "Learn More")

            # Log generated content
            self.logger.info(f"Generated popup content for {user_email}: {popup_title}")

            return {
                'user_email': user_email,
                'behaviour_data': user_behavior,
                'target_product': target_product,
                'user_stage': user_stage,
                'redirect_url': redirect_url,
                'popup_title': popup_title,
                'popup_text': popup_text,
                'button_text': button_text,
                'success': True,
                'error': None
            }

        except Exception as e:
            self.logger.error(f"Error generating popup content for {user_email}: {str(e)}")
            
            # Return fallback content
            return {
                'user_email': user_email,
                'behaviour_data': user_behavior,
                'target_product': target_product,
                'user_stage': user_stage,
                'redirect_url': redirect_url,
                'popup_title': f"Special Offer for {first_name}",
                'popup_text': f"Enhance your skills with {target_product}!",
                'button_text': "Learn More",
                'success': False,
                'error': str(e)
            }

    def generate_popup_content_batch(self, data_df: Optional[pd.DataFrame] = None,
                                   progress_callback: Optional[Callable] = None,
                                   max_workers: int = 5, batch_size: int = 50) -> pd.DataFrame:
        """
        Generate popup content for multiple users using parallel processing.

        Args:
            data_df: DataFrame with user data (optional, will load from CSV if not provided)
            progress_callback: Optional callback function for progress updates
            max_workers: Maximum number of worker threads
            batch_size: Batch size for processing

        Returns:
            DataFrame with generated popup content
        """
        try:
            # Load data if not provided
            if data_df is None:
                data_file = 'data/user_popup_data.csv'
                if not os.path.exists(data_file):
                    raise FileNotFoundError(f"User popup data file not found: {data_file}")
                
                data_df = pd.read_csv(data_file, usecols=['user_email', 'behaviour_data', 'target_product', 'user_stage', 'redirect_url'])
                self.logger.info(f"Loaded {len(data_df)} users from {data_file}")

            total_users = len(data_df)
            results = []

            # Process in batches with parallel execution
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                for batch_start in range(0, total_users, batch_size):
                    batch_end = min(batch_start + batch_size, total_users)
                    batch_df = data_df.iloc[batch_start:batch_end]
                    
                    # Submit batch tasks
                    future_to_user = {}
                    for idx, row in batch_df.iterrows():
                        # Extract first name from email
                        first_name = row['user_email'].split('@')[0].split('.')[0].capitalize()
                        
                        future = executor.submit(
                            self.generate_popup_content,
                            first_name=first_name,
                            user_behavior=row['behaviour_data'],
                            target_product=row['target_product'],
                            user_stage=row['user_stage'],
                            user_email=row['user_email'],
                            redirect_url=row['redirect_url']
                        )
                        future_to_user[future] = idx

                    # Collect results
                    for future in as_completed(future_to_user):
                        result = future.result()
                        results.append(result)
                        
                        # Update progress
                        if progress_callback:
                            progress = len(results) / total_users * 100
                            progress_callback(len(results), total_users, f"Generated popup content for {len(results)}/{total_users} users")

            # Convert results to DataFrame
            result_df = pd.DataFrame(results)
            
            # Save results to CSV
            output_file = 'data/popup_text.csv'
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            result_df.to_csv(output_file, index=False)
            
            self.logger.info(f"Popup content generation completed. Results saved to {output_file}")
            
            return result_df

        except Exception as e:
            self.logger.error(f"Error in batch popup content generation: {str(e)}")
            raise

    def generate_popup_content_from_crew(self, popup_prompt: str) -> Dict[str, Any]:
        """
        Generate popup content using CrewAI (for compatibility with existing crew manager).

        Args:
            popup_prompt: Formatted prompt for popup generation

        Returns:
            Dict containing popup content
        """
        try:
            if not self.client:
                raise ValueError("OpenAI API key not provided")

            # Generate content using OpenAI
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a marketing content generator. Generate personalized popup content in JSON format."},
                    {"role": "user", "content": popup_prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )

            # Extract content from response
            content = response.choices[0].message.content
            
            # Parse JSON response
            try:
                response_json = json.loads(content)
                return {
                    'popup_title': response_json.get('popup_title', 'Special Offer'),
                    'popup_text': response_json.get('popup_text', 'Check out our latest products!'),
                    'button_text': response_json.get('button_text', 'Learn More')
                }
            except json.JSONDecodeError:
                # Return fallback content
                return {
                    'popup_title': 'Special Offer',
                    'popup_text': 'Check out our latest products!',
                    'button_text': 'Learn More'
                }

        except Exception as e:
            self.logger.error(f"Error generating popup content from crew: {str(e)}")
            return {
                'popup_title': 'Welcome',
                'popup_text': 'Discover our amazing products!',
                'button_text': 'Explore'
            }


def load_user_popup_data(csv_file: str = 'data/user_popup_data.csv') -> Optional[pd.DataFrame]:
    """
    Load user popup data from CSV file.

    Args:
        csv_file: Path to the CSV file

    Returns:
        DataFrame with user popup data or None if not found
    """
    try:
        if os.path.exists(csv_file):
            df = pd.read_csv(csv_file)
            return df
        else:
            print(f"User popup data file not found: {csv_file}")
            return None
    except Exception as e:
        print(f"Error loading user popup data: {str(e)}")
        return None


def generate_single_popup_content(first_name: str, user_behavior: str, target_product: str,
                                 user_stage: str, api_key: str = None) -> Dict[str, Any]:
    """
    Generate popup content for a single user.

    Args:
        first_name: User's first name
        user_behavior: User's behavior data
        target_product: Target product
        user_stage: User's stage in the journey
        api_key: OpenAI API key

    Returns:
        Dict containing generated popup content
    """
    generator = PopupTextGenerator(api_key)
    
    return generator.generate_popup_content(
        first_name=first_name,
        user_behavior=user_behavior,
        target_product=target_product,
        user_stage=user_stage
    )


def generate_popup_content_batch(data_df: Optional[pd.DataFrame] = None,
                               progress_callback: Optional[Callable] = None,
                               max_workers: int = 5, batch_size: int = 50,
                               api_key: str = None) -> pd.DataFrame:
    """
    Generate popup content for multiple users using parallel processing.

    Args:
        data_df: DataFrame with user data
        progress_callback: Optional callback function for progress updates
        max_workers: Maximum number of worker threads
        batch_size: Batch size for processing
        api_key: OpenAI API key

    Returns:
        DataFrame with generated popup content
    """
    generator = PopupTextGenerator(api_key)
    
    return generator.generate_popup_content_batch(
        data_df=data_df,
        progress_callback=progress_callback,
        max_workers=max_workers,
        batch_size=batch_size
    )


def generate_popup_content_from_prompt(popup_prompt: str, api_key: str = None) -> Dict[str, Any]:
    """
    Generate popup content from a formatted prompt.

    Args:
        popup_prompt: Formatted prompt for popup generation
        api_key: OpenAI API key

    Returns:
        Dict containing popup content
    """
    generator = PopupTextGenerator(api_key)
    
    return generator.generate_popup_content_from_crew(popup_prompt)


def save_popup_results(results_df: pd.DataFrame, output_file: str = 'data/popup_text.csv') -> Dict[str, Any]:
    """
    Save popup generation results to CSV file.

    Args:
        results_df: DataFrame with popup results
        output_file: Output file path

    Returns:
        Dict with operation results
    """
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        results_df.to_csv(output_file, index=False)
        
        return {
            "success": True,
            "output_file": output_file,
            "records_saved": len(results_df),
            "message": f"Popup results saved to {output_file}"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": f"Failed to save popup results: {str(e)}"
        }
