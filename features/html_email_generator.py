"""
HTML Email Generator Backend Script
Aggregates all HTML email generation functionality including text to HTML conversion,
brand styling, CTA integration, and template processing.
"""
import os
import json
import logging
import re
import html
from typing import Dict, Any, Optional
from pathlib import Path

class HTMLEmailGenerator:
    """Generator for HTML email content with brand styling"""

    def __init__(self):
        """Initialize the HTML email generator"""
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the generator"""
        logger = logging.getLogger("openengage.html.email_generator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def load_brand_guidelines(self, organization_url: str = None) -> Dict[str, Any]:
        """
        Load brand guidelines from file.

        Args:
            organization_url: The organization URL to filter by

        Returns:
            Dict containing brand guidelines or default guidelines
        """
        guidelines_path = os.path.join('data', 'brand_guidelines.json')

        # Default brand guidelines
        default_guidelines = {
            "primary_color": "#2674ED",
            "secondary_color": "#F9C823",
            "accent_color": "#FF6B6B",
            "neutral_color": "#6C757D",
            "background_color": "#FFFFFF",
            "text_color": "#333333",
            "cta_type": "Button",
            "cta_size": "Medium",
            "button_style": "Rounded",
            "border_radius": "4px",
            "font": "Arial",
            "font_size": "16px",
            "font_weight": "Normal",
            "organization_url": organization_url or ""
        }

        try:
            if os.path.exists(guidelines_path):
                with open(guidelines_path, 'r') as f:
                    all_guidelines = json.load(f)

                if organization_url:
                    # Clean up the organization URL
                    clean_org_url = organization_url.rstrip('/').lower()
                    if clean_org_url.startswith('www.'):
                        clean_org_url = clean_org_url[4:]
                    elif clean_org_url.startswith('http://www.'):
                        clean_org_url = clean_org_url[11:]
                    elif clean_org_url.startswith('https://www.'):
                        clean_org_url = clean_org_url[12:]
                    elif clean_org_url.startswith('http://'):
                        clean_org_url = clean_org_url[7:]
                    elif clean_org_url.startswith('https://'):
                        clean_org_url = clean_org_url[8:]

                    # Look for exact match first
                    if clean_org_url in all_guidelines:
                        guidelines = all_guidelines[clean_org_url]
                        # Merge with defaults for any missing keys
                        for key, value in default_guidelines.items():
                            if key not in guidelines:
                                guidelines[key] = value
                        return guidelines

                # Return first organization's guidelines or default
                if isinstance(all_guidelines, dict):
                    if 'default_organization' in all_guidelines:
                        return all_guidelines['default_organization']
                    elif len(all_guidelines) > 0:
                        return next(iter(all_guidelines.values()))

        except Exception as e:
            self.logger.warning(f"Error loading brand guidelines: {str(e)}. Using default guidelines.")

        return default_guidelines

    def load_template_cta(self, template_name: str) -> Optional[str]:
        """
        Load CTA text for a specific template from the JSON file.

        Args:
            template_name: Name of the template to get CTA for

        Returns:
            CTA text if found, None otherwise
        """
        if not template_name:
            return None

        try:
            cta_file_path = 'data/templates/template_ctas.json'

            if not os.path.exists(cta_file_path):
                return None

            with open(cta_file_path, 'r', encoding='utf-8') as f:
                ctas = json.load(f)

            # Get CTA text for the specific template
            cta_text = ctas.get(template_name)

            if cta_text:
                # Clean up the CTA text
                cta_text = cta_text.strip().strip('"').strip("'")
                return cta_text

        except Exception as e:
            self.logger.warning(f"Error loading CTA for template {template_name}: {str(e)}")

        return None

    def get_cta_text_for_template(self, template_name: str, default_cta: str = 'Learn More') -> str:
        """
        Get CTA text for a template with fallback to default.

        Args:
            template_name: Name of the template to get CTA for
            default_cta: Default CTA text to use if template not found

        Returns:
            CTA text (either custom from JSON or default)
        """
        if not template_name:
            return default_cta

        # Try exact match first
        custom_cta = self.load_template_cta(template_name)
        if custom_cta:
            return custom_cta

        # If exact match fails, try pattern matching
        try:
            cta_file_path = 'data/templates/template_ctas.json'
            if os.path.exists(cta_file_path):
                with open(cta_file_path, 'r', encoding='utf-8') as f:
                    ctas = json.load(f)

                # Look for partial matches
                best_match = None
                max_score = 0

                for cta_template_name, cta_text in ctas.items():
                    # Simple scoring based on common words
                    template_words = set(template_name.lower().split('_'))
                    cta_words = set(cta_template_name.lower().split('_'))
                    common_words = template_words.intersection(cta_words)
                    score = len(common_words)

                    if score > max_score:
                        max_score = score
                        best_match = cta_text

                if best_match:
                    best_match = best_match.strip().strip('"').strip("'")
                    return best_match

        except Exception as e:
            self.logger.warning(f"Error in pattern matching for template {template_name}: {str(e)}")

        return default_cta

    def create_cta_button(self, cta_text: str, url: str, brand_guidelines: Dict[str, Any] = None) -> str:
        """
        Create an HTML CTA button using brand guidelines.

        Args:
            cta_text: Text for the CTA button
            url: URL for the button
            brand_guidelines: Dictionary containing brand guidelines

        Returns:
            HTML for a styled CTA button
        """
        # Default values
        bg_color = "#2674ED"
        text_color = "#FFFFFF"
        border_radius = "4px"
        padding = "12px 24px"
        font_family = "Arial, sans-serif"
        font_size = "16px"
        font_weight = "bold"
        using_gradient = False
        gradient_css = ""

        # Apply brand guidelines if available
        if brand_guidelines:
            # Get button color based on the selected type
            button_color_type = brand_guidelines.get("button_color_type", "Primary Color")

            if button_color_type == "Primary Color":
                bg_color = brand_guidelines.get("primary_color", bg_color)
            elif button_color_type == "Secondary Color":
                bg_color = brand_guidelines.get("secondary_color", bg_color)
            elif button_color_type == "Accent Color":
                bg_color = brand_guidelines.get("accent_color", bg_color)
            elif button_color_type == "Gradient":
                primary = brand_guidelines.get("primary_color", "#2674ED")
                secondary = brand_guidelines.get("secondary_color", "#F9C823")
                gradient_css = f"linear-gradient(135deg, {primary} 0%, {secondary} 100%)"
                using_gradient = True

            # Apply button style (rounded vs cornered)
            if brand_guidelines.get("button_style") == "Rounded":
                border_radius = brand_guidelines.get("border_radius", border_radius)
            else:
                border_radius = "0px"

            # Apply button size
            cta_size = brand_guidelines.get("cta_size", "Medium")
            if cta_size == "Small":
                padding = "8px 16px"
                font_size = "14px"
            elif cta_size == "Large":
                padding = "16px 32px"
                font_size = "18px"

            # Apply font settings
            font_family = f"{brand_guidelines.get('font', 'Arial')}, sans-serif"
            font_size = brand_guidelines.get("font_size", font_size)
            font_weight = brand_guidelines.get("font_weight", "Bold").lower()

        # Create button HTML
        button_style = f"""
            display: inline-block;
            color: {text_color} !important;
            padding: {padding};
            text-decoration: none;
            border-radius: {border_radius};
            font-weight: {font_weight};
            font-family: {font_family};
            font-size: {font_size};
            text-align: center;
            margin: 20px 0;
            {f"background: {gradient_css} !important;" if using_gradient else f"background-color: {bg_color} !important;"}
            {'' if using_gradient else f'border: 1px solid {bg_color};'}
        """.strip()

        return f'''
        <div style="text-align: center; margin: 20px 0;">
            <a href="{url}" style="{button_style}" class="button">
                {cta_text}
            </a>
        </div>
        '''

    def process_inline_formatting(self, text: str) -> str:
        """
        Process inline text formatting like bold, italic, and links.

        Args:
            text: Plain text content

        Returns:
            HTML formatted text with inline styling
        """
        if not text:
            return text

        try:
            # Process bold text (surrounded by ** or __)
            text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
            text = re.sub(r'__(.*?)__', r'<strong>\1</strong>', text)

            # Process italic text (surrounded by * or _)
            text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)

            # Special case for underscores to avoid matching in URLs
            pattern = r'(?<!\w)_((?!https?://)[^_]+?)_(?!\w)'
            text = re.sub(pattern, r'<em>\1</em>', text)

            # Process markdown-style links [text](url)
            text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', text)

        except Exception as e:
            self.logger.warning(f"Error processing inline formatting: {str(e)}")

        return text

    def process_paragraphs(self, content: str, sender_name: str = None,
                          communication_settings: Dict[str, Any] = None, product_name: str = None,
                          product_url: str = None, recipient_first_name: str = None,
                          brand_guidelines: Dict[str, Any] = None) -> str:
        """
        Process text content into HTML paragraphs.

        Args:
            content: Plain text content
            sender_name: Name of the sender for salutation replacement
            communication_settings: Dictionary containing UTM parameters
            product_name: Name of the product for link text
            product_url: URL of the product to link the product name
            recipient_first_name: First name of the recipient for personalized greeting
            brand_guidelines: Dictionary containing brand guidelines

        Returns:
            HTML formatted paragraphs
        """
        if not content:
            return ""

        # Split content into paragraphs
        paragraphs = content.split('\n\n')
        html_paragraphs = []

        for para in paragraphs:
            if not para.strip():
                continue

            # Clean up the paragraph
            para = para.strip()

            # Replace sender name placeholders
            if sender_name:
                para = para.replace("[Sender Name]", sender_name)
                para = para.replace("{sender_name}", sender_name)

            # Replace recipient name placeholders
            if recipient_first_name:
                para = para.replace("[First Name]", recipient_first_name)
                para = para.replace("{first_name}", recipient_first_name)
                para = para.replace("[Recipient Name]", recipient_first_name)
                para = para.replace("{recipient_name}", recipient_first_name)

            # Replace product name placeholders and create links
            if product_name:
                if product_url:
                    # Create a link for the product name
                    product_link = f'<a href="{product_url}" style="color: {brand_guidelines.get("primary_color", "#0366d6") if brand_guidelines else "#0366d6"};">{product_name}</a>'
                    para = para.replace(f"[{product_name}]", product_link)
                    para = para.replace(f"{{{product_name}}}", product_link)
                    para = para.replace(product_name, product_link)
                else:
                    para = para.replace(f"[{product_name}]", product_name)
                    para = para.replace(f"{{{product_name}}}", product_name)

            # Process URLs in the text
            para = self.process_urls_in_text(para, communication_settings, product_name, brand_guidelines)

            # Process inline formatting (bold, italic)
            para = self.process_inline_formatting(para)

            html_paragraphs.append(f'<p style="margin: 10px 0; line-height: 1.5;">{para}</p>')

        return '\n'.join(html_paragraphs)

    def create_html_email_template(self, body_content: str, company_name: str = "OpenEngage",
                                  product_url: str = None, recipient_email: str = None,
                                  brand_guidelines: Dict[str, Any] = None,
                                  template_name: str = None) -> str:
        """
        Create a complete HTML email template using brand guidelines.

        Args:
            body_content: HTML content for the email body
            company_name: Name of the company for footer
            product_url: URL for CTA button
            recipient_email: Email address for unsubscribe link
            brand_guidelines: Dictionary containing brand guidelines
            template_name: Template name for CTA lookup

        Returns:
            Complete HTML email with embedded styling
        """
        # Process CTA placement
        if product_url:
            try:
                # Get CTA text using the centralized helper function
                cta_text = self.get_cta_text_for_template(template_name, 'Learn More')

                # Create the CTA button
                cta_button = self.create_cta_button(cta_text, product_url, brand_guidelines)

                # Try to insert after the third paragraph
                paragraphs = list(re.split(r'</p>\s*<p[^>]*>', body_content))

                if len(paragraphs) >= 3:
                    # Insert CTA after third paragraph
                    first_part = '</p><p>'.join(paragraphs[:3]) + '</p>'
                    remaining_part = '<p>' + '</p><p>'.join(paragraphs[3:])
                    body_content = first_part + cta_button + remaining_part
                elif len(paragraphs) == 2:
                    # Insert CTA after first paragraph
                    first_part = '</p><p>'.join(paragraphs[:1]) + '</p>'
                    remaining_part = '<p>' + '</p><p>'.join(paragraphs[1:])
                    body_content = first_part + cta_button + remaining_part
                else:
                    # Single paragraph or no paragraphs, try to insert in the middle
                    content = body_content.strip()
                    if content:
                        sentences = re.split(r'(?<=[.!?])\s+', content)
                        if len(sentences) > 2:
                            midpoint = len(sentences) // 2
                            first_half = ' '.join(sentences[:midpoint])
                            second_half = ' '.join(sentences[midpoint:])
                            body_content = first_half + cta_button + second_half
                        else:
                            body_content = content + cta_button

            except Exception as e:
                # Fallback: just append the button
                try:
                    cta_text = self.get_cta_text_for_template(template_name, 'Learn More')
                    cta_button = self.create_cta_button(cta_text, product_url, brand_guidelines)
                    body_content = body_content + cta_button
                except Exception as fallback_e:
                    self.logger.warning(f"Error creating CTA button: {str(fallback_e)}")

        # Default styling values
        font_family = "Arial, sans-serif"
        font_size = "16px"
        text_color = "#333333"
        link_color = "#0366d6"
        bg_color = "#f4f4f4"
        container_bg = "#ffffff"
        button_bg_color = "#2674ED"
        using_gradient = False
        button_gradient = ""

        # Apply brand guidelines if available
        if brand_guidelines:
            # Typography settings
            font_family = f"{brand_guidelines.get('font', 'Arial')}, sans-serif"
            font_size = brand_guidelines.get('font_size', '16px')

            # Color settings
            text_color = brand_guidelines.get('text_color', text_color)
            link_color = brand_guidelines.get('primary_color', link_color)
            bg_color = brand_guidelines.get('background_color', bg_color)
            container_bg = brand_guidelines.get('background_color', bg_color)

            # Get button color based on the selected type
            button_color_type = brand_guidelines.get("button_color_type", "Primary Color")

            if button_color_type == "Primary Color":
                button_bg_color = brand_guidelines.get("primary_color", button_bg_color)
            elif button_color_type == "Secondary Color":
                button_bg_color = brand_guidelines.get("secondary_color", button_bg_color)
            elif button_color_type == "Accent Color":
                button_bg_color = brand_guidelines.get("accent_color", button_bg_color)
            elif button_color_type == "Gradient":
                primary = brand_guidelines.get("primary_color", "#2674ED")
                secondary = brand_guidelines.get("secondary_color", "#F9C823")
                button_gradient = f"linear-gradient(135deg, {primary} 0%, {secondary} 100%)"
                using_gradient = True

        # Create unsubscribe link
        unsubscribe_link = ""
        if recipient_email:
            unsubscribe_url = f"mailto:<EMAIL>?subject=Unsubscribe&body=Please unsubscribe {recipient_email}"
            unsubscribe_link = f'<a href="{unsubscribe_url}" style="color: #777;">Unsubscribe</a>'

        return f'''
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Email</title>
            <style>
                body {{
                    font-family: {font_family};
                    line-height: 1.6;
                    color: {text_color};
                    margin: 0;
                    padding: 0;
                    background-color: {bg_color};
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: {container_bg};
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }}
                .email-body {{
                    padding: 10px 0;
                    font-size: {font_size};
                }}
                .email-footer {{
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #eee;
                    font-size: 12px;
                    color: #777;
                    text-align: center;
                }}
                a {{
                    color: {link_color};
                    text-decoration: none;
                }}
                a:hover {{
                    text-decoration: underline;
                }}
                .button {{
                    display: inline-block;
                    color: #FFFFFF !important;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: {brand_guidelines.get('border_radius', '4px') if brand_guidelines and brand_guidelines.get('button_style') == 'Rounded' else '0px'};
                    font-weight: bold;
                    text-align: center;
                    {f"background: {button_gradient} !important;" if using_gradient and button_gradient else f"background-color: {button_bg_color} !important;"}
                    {'' if using_gradient else 'border: 1px solid ' + button_bg_color + ';'}
                }}
                .button:hover {{
                    opacity: 0.9;
                }}
                p {{
                    font-family: {font_family};
                    font-size: {font_size};
                    color: {text_color};
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-body">
                    {body_content}
                </div>
                <div class="email-footer">
                    <p>© 2024 {company_name}. All rights reserved.</p>
                    {f"<p>{unsubscribe_link}</p>" if unsubscribe_link else ""}
                </div>
            </div>
        </body>
        </html>
        '''

    def text_to_html(self, email_content: Dict[str, Any], product_url: Optional[str] = None,
                     product_name: Optional[str] = None, communication_settings: Optional[Dict[str, Any]] = None,
                     recipient_email: Optional[str] = None, recipient_first_name: Optional[str] = None,
                     brand_guidelines: Optional[Dict[str, Any]] = None, template_name: Optional[str] = None) -> str:
        """
        Convert plain text email content to HTML format with brand styling and CTA integration.

        Args:
            email_content: Dictionary containing email content with 'subject' and 'content' keys
            product_url: URL of the product for linking
            product_name: Name of the product for link text
            communication_settings: Dictionary containing UTM parameters and sender info
            recipient_email: Email address of the recipient for unsubscribe link
            recipient_first_name: First name of the recipient for personalized greeting
            brand_guidelines: Dictionary containing brand guidelines for styling
            template_name: Name of the template for CTA lookup

        Returns:
            HTML formatted email content with embedded styling and CTA buttons
        """
        if not email_content or not isinstance(email_content, dict):
            return ""

        # Extract content
        content = email_content.get('content', '')

        if not content:
            return ""

        # Remove any "Body:" or "Email Body:" prefixes
        content = re.sub(r'^\s*(?:Body|Email Body)\s*:\s*', '', content)

        # Check if content already contains HTML tags
        contains_html = re.search(r'<[a-z]+[^>]*>', content, re.IGNORECASE) is not None

        # Only escape HTML special characters if the content doesn't already contain HTML
        if not contains_html:
            content = html.escape(content)

        # Get sender name from communication settings
        sender_name = None
        if communication_settings:
            sender_name = communication_settings.get('sender_name', 'OpenEngage Team')

        # Get organization URL for brand guidelines
        organization_url = None
        if communication_settings:
            organization_url = communication_settings.get('organization_url')

        # Use provided brand guidelines or load from file
        if brand_guidelines is None:
            brand_guidelines = self.load_brand_guidelines(organization_url)

        # Process the content
        paragraphs = self.process_paragraphs(
            content, sender_name, communication_settings, product_name,
            product_url, recipient_first_name, brand_guidelines
        )

        # Get company name for footer
        company_name = "OpenEngage"
        if communication_settings and 'sender_name' in communication_settings:
            company_name = communication_settings.get('sender_name')

        # Create the full HTML email
        html_email = self.create_html_email_template(
            paragraphs, company_name, product_url, recipient_email, brand_guidelines, template_name
        )

        return html_email

    def process_urls_in_text(self, text: str, communication_settings: Dict[str, Any] = None,
                           product_name: str = None, brand_guidelines: Dict[str, Any] = None) -> str:
        """
        Process plain URLs in text and add UTM parameters if needed.

        Args:
            text: Text content that may contain URLs
            communication_settings: Dictionary containing UTM parameters
            product_name: Name of the product to use in link text
            brand_guidelines: Dictionary containing brand guidelines

        Returns:
            Text with processed URLs
        """
        if not communication_settings:
            return text

        # Default link color
        link_color = "#0366d6"

        # Apply brand guidelines if available
        if brand_guidelines:
            link_color = brand_guidelines.get("primary_color", link_color)

        # UTM parameters
        utm_params = []
        if communication_settings.get("utm_source"):
            utm_params.append(f"utm_source={communication_settings['utm_source']}")
        if communication_settings.get("utm_medium"):
            utm_params.append(f"utm_medium={communication_settings['utm_medium']}")
        if communication_settings.get("utm_campaign"):
            utm_params.append(f"utm_campaign={communication_settings['utm_campaign']}")
        if communication_settings.get("utm_content"):
            utm_params.append(f"utm_content={communication_settings['utm_content']}")

        utm_string = "&".join(utm_params) if utm_params else ""

        # Process URLs
        def replace_url(match):
            url = match.group(0)
            if utm_string:
                separator = "&" if "?" in url else "?"
                url_with_utm = f"{url}{separator}{utm_string}"
            else:
                url_with_utm = url

            return f'<a href="{url_with_utm}" style="color: {link_color};">{url}</a>'

        # Match URLs
        url_pattern = r'https?://[^\s<>"\']+[^\s<>"\'.,;:!?)]'
        text = re.sub(url_pattern, replace_url, text)

        return text


# Main functions for external use

def convert_text_to_html(email_content: Dict[str, Any], product_url: str = None,
                        product_name: str = None, communication_settings: Dict[str, Any] = None,
                        recipient_email: str = None, recipient_first_name: str = None,
                        brand_guidelines: Dict[str, Any] = None, template_name: str = None) -> str:
    """
    Convert plain text email content to HTML format with brand styling.

    Args:
        email_content: Dictionary containing email content
        product_url: URL of the product for linking
        product_name: Name of the product for link text
        communication_settings: Dictionary containing UTM parameters and sender info
        recipient_email: Email address of the recipient
        recipient_first_name: First name of the recipient
        brand_guidelines: Dictionary containing brand guidelines
        template_name: Name of the template for CTA lookup

    Returns:
        HTML formatted email content
    """
    generator = HTMLEmailGenerator()

    return generator.text_to_html(
        email_content=email_content,
        product_url=product_url,
        product_name=product_name,
        communication_settings=communication_settings,
        recipient_email=recipient_email,
        recipient_first_name=recipient_first_name,
        brand_guidelines=brand_guidelines,
        template_name=template_name
    )


def create_html_email_from_text(text_content: str, subject: str = "Email Subject",
                               product_url: str = None, product_name: str = None,
                               sender_name: str = "OpenEngage Team", recipient_email: str = None,
                               recipient_first_name: str = None, organization_url: str = None,
                               template_name: str = None, utm_params: Dict[str, str] = None) -> str:
    """
    Create a complete HTML email from plain text content.

    Args:
        text_content: Plain text email content
        subject: Email subject line
        product_url: URL of the product for linking
        product_name: Name of the product
        sender_name: Name of the sender
        recipient_email: Email address of the recipient
        recipient_first_name: First name of the recipient
        organization_url: Organization URL for brand guidelines
        template_name: Template name for CTA lookup
        utm_params: Dictionary containing UTM parameters

    Returns:
        Complete HTML email
    """
    generator = HTMLEmailGenerator()

    # Create email content dictionary
    email_content = {
        'subject': subject,
        'content': text_content
    }

    # Create communication settings
    communication_settings = {
        'sender_name': sender_name,
        'organization_url': organization_url
    }

    # Add UTM parameters if provided
    if utm_params:
        communication_settings.update(utm_params)

    # Load brand guidelines
    brand_guidelines = generator.load_brand_guidelines(organization_url)

    return generator.text_to_html(
        email_content=email_content,
        product_url=product_url,
        product_name=product_name,
        communication_settings=communication_settings,
        recipient_email=recipient_email,
        recipient_first_name=recipient_first_name,
        brand_guidelines=brand_guidelines,
        template_name=template_name
    )


def create_cta_button_html(cta_text: str, url: str, organization_url: str = None) -> str:
    """
    Create a standalone CTA button HTML.

    Args:
        cta_text: Text for the CTA button
        url: URL for the button
        organization_url: Organization URL for brand guidelines

    Returns:
        HTML for a styled CTA button
    """
    generator = HTMLEmailGenerator()
    brand_guidelines = generator.load_brand_guidelines(organization_url)

    return generator.create_cta_button(cta_text, url, brand_guidelines)


def load_brand_guidelines_for_organization(organization_url: str = None) -> Dict[str, Any]:
    """
    Load brand guidelines for a specific organization.

    Args:
        organization_url: Organization URL

    Returns:
        Dictionary containing brand guidelines
    """
    generator = HTMLEmailGenerator()
    return generator.load_brand_guidelines(organization_url)


def get_template_cta_text(template_name: str, default_cta: str = 'Learn More') -> str:
    """
    Get CTA text for a specific template.

    Args:
        template_name: Name of the template
        default_cta: Default CTA text if template not found

    Returns:
        CTA text for the template
    """
    generator = HTMLEmailGenerator()
    return generator.get_cta_text_for_template(template_name, default_cta)


def save_template_cta(template_name: str, cta_text: str) -> bool:
    """
    Save CTA text for a template.

    Args:
        template_name: Name of the template
        cta_text: CTA text to save

    Returns:
        True if successful, False otherwise
    """
    try:
        # Load existing CTAs
        cta_file_path = 'data/templates/template_ctas.json'
        ctas = {}

        if os.path.exists(cta_file_path):
            try:
                with open(cta_file_path, 'r') as f:
                    ctas = json.load(f)
            except Exception:
                ctas = {}

        # Update with new CTA
        ctas[template_name] = cta_text

        # Ensure directory exists
        os.makedirs('data/templates', exist_ok=True)

        # Save updated CTAs
        with open(cta_file_path, 'w') as f:
            json.dump(ctas, f, indent=4)

        return True

    except Exception:
        return False


def load_all_template_ctas() -> Dict[str, str]:
    """
    Load all template CTAs from the JSON file.

    Returns:
        Dictionary mapping template names to CTA texts
    """
    cta_file_path = 'data/templates/template_ctas.json'
    try:
        if os.path.exists(cta_file_path):
            with open(cta_file_path, 'r') as f:
                return json.load(f)
        return {}
    except Exception:
        return {}


def process_email_batch_to_html(email_batch: list, brand_guidelines: Dict[str, Any] = None,
                               communication_settings: Dict[str, Any] = None) -> list:
    """
    Process a batch of emails and convert them to HTML.

    Args:
        email_batch: List of email dictionaries
        brand_guidelines: Brand guidelines to apply
        communication_settings: Communication settings

    Returns:
        List of emails with HTML content added
    """
    generator = HTMLEmailGenerator()

    processed_emails = []

    for email in email_batch:
        try:
            html_content = generator.text_to_html(
                email_content=email,
                product_url=email.get('product_url'),
                product_name=email.get('product_name'),
                communication_settings=communication_settings,
                recipient_email=email.get('recipient_email'),
                recipient_first_name=email.get('recipient_first_name'),
                brand_guidelines=brand_guidelines,
                template_name=email.get('template_name')
            )

            # Add HTML content to email
            email_copy = email.copy()
            email_copy['html_content'] = html_content
            processed_emails.append(email_copy)

        except Exception as e:
            # Add error information but keep the email in the batch
            email_copy = email.copy()
            email_copy['html_content'] = f"<p>Error generating HTML: {str(e)}</p>"
            email_copy['html_error'] = str(e)
            processed_emails.append(email_copy)

    return processed_emails
