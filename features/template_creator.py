"""
Backend module for template creation and generation.

This module compiles all the template creation functionality from multiple scripts
into a single backend service. It handles stage-based template generation, emoji
inclusion, and template generation parameters.

Functions:
- create_templates(product_stages, stage_details, num_templates_per_stage, include_emojis, product_data): Main function for template generation
- generate_stage_templates(stage, stage_details, num_templates, product_data, settings): Generate templates for specific stage
- load_user_journey(product_name): Load user journey stages for product
- save_templates(templates, stage): Save generated templates to storage
- load_communication_settings(organization_url): Load communication settings
"""

import os
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dotenv import load_dotenv
from openai import OpenAI

# Load environment variables
load_dotenv()


class TemplateGenerator:
    """Core template generation engine."""
    
    def __init__(self):
        self.name = "Template Generator"
        self.description = "Generates email templates for different stages with customizable parameters"
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def generate_template(self, stage: str, stage_details: Dict[str, Any], product_data: Dict[str, Any], 
                         settings: Dict[str, Any], emoji_instruction: str = "") -> Dict[str, Any]:
        """
        Generate a single template for a specific stage.
        
        Args:
            stage (str): Stage name
            stage_details (Dict[str, Any]): Stage description and goal information
            product_data (Dict[str, Any]): Product information
            settings (Dict[str, Any]): Communication settings
            emoji_instruction (str): Instructions for emoji usage
            
        Returns:
            Dict[str, Any]: Generated template data
        """
        try:
            # Create task description for template generation
            task_description = f"""Generate an email template for the {stage} stage using these details:

Product Details:
Target Product: {product_data.get('Product_Name', '')}
Target Product URL: {product_data.get('Product_URL', '')}
Target Product Summary: {product_data.get('Product_Summary', '')}
Target Product Features:
{chr(10).join('- ' + feature for feature in product_data.get('Product_Features', []))}

Stage Details:
Current Stage: {stage_details.get('current_stage', stage)}
Goal Stage: {stage_details.get('goal_stage', '')}
Stage Description: {stage_details.get('description', '')}

Communication Settings:
Tone: {settings.get('tone', 'professional')}
Style: {settings.get('style', 'friendly')}
Length: {settings.get('length', '100-150 words')}
Sender Name: {settings.get('sender_name', 'OpenEngage Team')}
Brand Personality: {settings.get('brand_personality', 'Professional')}
Brand Tone of Voice: {settings.get('tone_of_voice', 'Professional, Informative')}

Instructions to make this email effective:
{emoji_instruction}

Instructions:
1. Write an engaging email that moves the user from {stage_details.get('current_stage', stage)} to {stage_details.get('goal_stage', '')}
2. Use the specified tone and style
3. Incorporate the brand personality traits into the messaging
4. Use the brand tone of voice to guide the writing style
5. Include relevant product features and benefits
6. Add UTM-tagged links where appropriate
7. Keep the email length within the specified range
8. Make the subject line compelling and relevant to the stage
9. Include a clear call-to-action that aligns with the goal stage

Return the email in this exact format:
Subject: [Your subject line here]

Body: [Your email body content here]"""

            # Generate template using OpenAI
            response = self.client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {
                        "role": "system",
                        "content": """You are an expert email marketing copywriter who creates compelling, stage-specific email templates.
                        
                        Your templates should:
                        - Be highly engaging and personalized to the user's journey stage
                        - Include compelling subject lines that drive opens
                        - Have clear, action-oriented content that moves users to the next stage
                        - Incorporate the brand's personality and tone of voice
                        - Include relevant product information naturally
                        - Have strong calls-to-action
                        - Follow email marketing best practices
                        
                        Always return emails in the exact format requested with Subject: and Body: sections."""
                    },
                    {
                        "role": "user",
                        "content": task_description
                    }
                ],
                temperature=0.7,
                max_tokens=1024
            )
            
            # Parse the response
            template_content = response.choices[0].message.content.strip()
            
            # Parse subject and body
            subject = ""
            body = ""
            
            if "Subject:" in template_content:
                parts = template_content.split("Subject:", 1)
                if len(parts) > 1:
                    template_parts = parts[1].split("Body:", 1)
                    if len(template_parts) > 1:
                        subject = template_parts[0].strip()
                        body = template_parts[1].strip()
                    else:
                        subject = template_parts[0].strip()
                        body = ""
            
            # If parsing failed, use the entire content as body
            if not subject and not body:
                body = template_content
                subject = f"Email for {stage}"
            
            # Process emoji settings
            subject, body = self._process_emoji_settings(subject, body, settings)
            
            # Create template structure
            template = {
                "subject": subject,
                "body": body
            }
            
            return template
            
        except Exception as e:
            # Return default template on error
            return {
                "subject": f"Email for {stage}",
                "body": f"Error generating template: {str(e)}"
            }
    
    def _process_emoji_settings(self, subject: str, body: str, settings: Dict[str, Any]) -> tuple:
        """
        Process emoji settings to add or remove emojis based on configuration.
        
        Args:
            subject (str): Email subject line
            body (str): Email body content
            settings (Dict[str, Any]): Communication settings
            
        Returns:
            tuple: Processed (subject, body)
        """
        # Get emoji settings
        use_emojis_subject = settings.get("use_emojis_subject", False)
        use_emojis_body = settings.get("use_emojis_body", False)
        
        # For backward compatibility
        if "use_emojis" in settings and "use_emojis_subject" not in settings:
            use_emojis = settings.get("use_emojis", False)
            use_emojis_subject = use_emojis
            use_emojis_body = use_emojis
        
        # Emoji pattern for detection and removal
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"      # Emoticons
            "\U0001F300-\U0001F5FF"      # Symbols & pictographs
            "\U0001F680-\U0001F6FF"      # Transport & map symbols
            "\U0001F1E0-\U0001F1FF"      # Flags (iOS)
            "\U00002600-\U000026FF"      # Miscellaneous
            "\U00002700-\U000027BF"      # Dingbats
            "\U0000FE0F"                 # Variation Selector (VS-16)
            "\U0000200D"                 # Zero Width Joiner
            "]", 
            re.UNICODE
        )
        
        # Remove emojis from subject if setting is disabled
        if not use_emojis_subject and subject:
            subject = emoji_pattern.sub('', subject).strip()
        
        # Remove emojis from body if setting is disabled
        if not use_emojis_body and body:
            body = emoji_pattern.sub('', body).strip()
        
        return subject, body


def load_user_journey(product_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Load user journey stages for a specific product or return default journey.
    
    Args:
        product_name (str, optional): Name of the product
        
    Returns:
        List[Dict[str, Any]]: List of journey stages
    """
    try:
        with open('data/user_journey.json', 'r') as f:
            journeys = json.load(f)
            if not isinstance(journeys, dict):
                # Convert old format to dict with default journey
                journeys = {"default": journeys if isinstance(journeys, list) else []}

            if product_name:
                # Return journey for specific product or default
                return journeys.get(product_name, journeys.get("default", get_default_journey()))

            # Return default journey
            return journeys.get("default", get_default_journey())
    except (FileNotFoundError, json.JSONDecodeError):
        return get_default_journey()


def get_default_journey() -> List[Dict[str, Any]]:
    """
    Return the default user journey stages.
    
    Returns:
        List[Dict[str, Any]]: Default journey stages
    """
    return [
        {
            "s_no": 1,
            "current_stage": "New Visitor",
            "description": "User is not aware of the product",
            "goal_stage": "Product Page Viewed"
        },
        {
            "s_no": 2,
            "current_stage": "Product Page Viewed",
            "description": "User has viewed product details",
            "goal_stage": "Product Lead Generated"
        },
        {
            "s_no": 3,
            "current_stage": "Product Lead Generated",
            "description": "User has shown interest in the product",
            "goal_stage": "Product Purchased"
        },
        {
            "s_no": 4,
            "current_stage": "Product Purchased",
            "description": "User has completed the purchase",
            "goal_stage": "Thank You"
        }
    ]


def load_communication_settings(organization_url: Optional[str] = None, sender_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Load communication settings for a specific organization or sender.
    
    Args:
        organization_url (str, optional): URL of the organization
        sender_name (str, optional): Name of the sender
        
    Returns:
        Dict[str, Any]: Communication settings
    """
    try:
        # Try to load from file first
        with open('data/communication_settings.json', 'r') as f:
            settings_list = json.load(f)
            if not isinstance(settings_list, list):
                settings_list = [settings_list]  # Convert old format to list

            # First try to find by organization_url if provided
            if organization_url:
                for settings in settings_list:
                    if settings.get("organization_url") == organization_url:
                        return settings

            # Then try to find by sender_name if provided
            if sender_name:
                for settings in settings_list:
                    if settings.get("sender_name") == sender_name:
                        return settings

            # Return first settings if available
            if settings_list:
                return settings_list[0]

    except (FileNotFoundError, json.JSONDecodeError):
        pass

    # Return default settings
    return {
        "tone": "professional",
        "style": "friendly",
        "length": "100-150 words",
        "sender_name": "OpenEngage Team",
        "brand_personality": "Professional",
        "tone_of_voice": "Professional, Informative",
        "use_emojis_subject": False,
        "use_emojis_body": False,
        "use_emojis": False,
        "utm_medium": "Email"
    }


def generate_stage_templates(stage: str, stage_details: Dict[str, Any], num_templates: int,
                           product_data: Dict[str, Any], settings: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Generate multiple templates for a specific stage.

    Args:
        stage (str): Stage name
        stage_details (Dict[str, Any]): Stage description and goal information
        num_templates (int): Number of templates to generate
        product_data (Dict[str, Any]): Product information
        settings (Dict[str, Any]): Communication settings

    Returns:
        List[Dict[str, Any]]: List of generated template data
    """
    generator = TemplateGenerator()
    templates = []

    # Handle emoji instructions
    use_emojis_subject = settings.get("use_emojis_subject", False)
    use_emojis_body = settings.get("use_emojis_body", False)

    # For backward compatibility
    if "use_emojis" in settings and "use_emojis_subject" not in settings:
        use_emojis = settings.get("use_emojis", False)
        use_emojis_subject = use_emojis
        use_emojis_body = use_emojis

    emoji_instruction = ""
    if use_emojis_subject and use_emojis_body:
        emoji_instruction = "Include relevant and engaging emojis in both the subject line and throughout the email body to make it more visually appealing and engaging."
    elif use_emojis_subject:
        emoji_instruction = "Include relevant and engaging emojis in the subject line only. Strictly DO NOT include emojis in the email body."
    elif use_emojis_body:
        emoji_instruction = "Include relevant and engaging emojis throughout the email body only. Strictly DO NOT include emojis in the subject line."

    # Generate templates
    for i in range(num_templates):
        template_name = f"{product_data['Product_Name'].replace(' ', '_')}_{stage.replace(' ', '_')}_{i + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Generate template
        template = generator.generate_template(stage, stage_details, product_data, settings, emoji_instruction)

        # Create template data structure
        template_data = {
            "template_name": template_name,
            "template": template,
            "product_data": product_data,
            "settings": settings,
            "channels": [settings.get("utm_medium", "Email")],
            "stage": stage,
            "generated_at": datetime.now().isoformat(),
            "template_number": i + 1,
            "verified": True,
            "tags": [stage.lower().replace(' ', '_'), product_data.get('Product_Name', '').lower().replace(' ', '_')],
            # Store detailed information about emoji usage
            "has_emojis_subject": settings.get("use_emojis_subject", False),
            "has_emojis_body": settings.get("use_emojis_body", False),
            # Keep the original field for backward compatibility
            "has_emojis": (settings.get("use_emojis_subject", False) or settings.get("use_emojis_body", False))
        }

        templates.append(template_data)

    return templates


def save_templates(templates: List[Dict[str, Any]], stage: str) -> bool:
    """
    Save generated templates to storage.

    Args:
        templates (List[Dict[str, Any]]): List of template data to save
        stage (str): Stage name for file organization

    Returns:
        bool: True if save successful, False otherwise
    """
    try:
        # Ensure templates directory exists
        os.makedirs('data/templates', exist_ok=True)

        # Create stage file path
        stage_file = f'data/templates/{stage.lower().replace(" ", "_")}.json'

        # Load existing templates for this stage
        existing_templates = []
        try:
            with open(stage_file, 'r') as f:
                existing_templates = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            existing_templates = []

        # Add new templates
        existing_templates.extend(templates)

        # Save updated templates
        with open(stage_file, 'w') as f:
            json.dump(existing_templates, f, indent=2)

        return True

    except Exception as e:
        print(f"Error saving templates: {str(e)}")
        return False


def load_templates(stage: str, organization_url: Optional[str] = None, product_name: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Load templates for a specific stage.

    Args:
        stage (str): Stage name
        organization_url (str, optional): Filter by organization URL
        product_name (str, optional): Filter by product name

    Returns:
        List[Dict[str, Any]]: List of template data
    """
    try:
        stage_file = f'data/templates/{stage.lower().replace(" ", "_")}.json'

        if not os.path.exists(stage_file):
            return []

        with open(stage_file, 'r') as f:
            templates = json.load(f)

        # Apply filters
        if organization_url:
            templates = [
                t for t in templates
                if t.get('product_data', {}).get('Company_URL', '') == organization_url or
                   t.get('product_data', {}).get('organization_url', '') == organization_url
            ]

        if product_name:
            templates = [
                t for t in templates
                if t.get('product_data', {}).get('Product_Name', '') == product_name
            ]

        # Exclude templates with pruning_flag = 1
        templates = [
            t for t in templates
            if t.get('performance', {}).get('pruning_flag', 0) != 1
        ]

        return templates

    except (FileNotFoundError, json.JSONDecodeError):
        return []


def create_templates(product_stages: List[str], stage_details: Dict[str, Dict[str, Any]],
                    num_templates_per_stage: int, include_emojis: Dict[str, bool],
                    product_data: Dict[str, Any], organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Main function that creates templates for multiple product stages.

    This function:
    1. Loads communication settings for the organization
    2. Processes emoji inclusion settings
    3. Generates templates for each stage
    4. Saves templates to storage
    5. Returns generation results

    Args:
        product_stages (List[str]): List of stage names to generate templates for
        stage_details (Dict[str, Dict[str, Any]]): Stage information including descriptions and goals
        num_templates_per_stage (int): Number of templates to generate per stage
        include_emojis (Dict[str, bool]): Emoji inclusion settings with keys:
            - use_emojis_subject: Include emojis in subject lines
            - use_emojis_body: Include emojis in email body
        product_data (Dict[str, Any]): Product information including:
            - Product_Name, Company_Name, Product_URL, Type_of_Product
            - Product_Features, Product_Summary, Priority, etc.
        organization_url (str, optional): Organization URL for loading settings

    Returns:
        Dict[str, Any]: Generation results containing:
            - success: Boolean indicating overall success
            - total_templates: Total number of templates generated
            - stage_results: Results for each stage
            - generated_templates: All generated template data
            - errors: Any errors encountered
    """
    try:
        # Load communication settings
        settings = load_communication_settings(organization_url=organization_url)

        # Update settings with emoji preferences
        settings.update(include_emojis)

        # Initialize results
        results = {
            "success": True,
            "total_templates": 0,
            "stage_results": {},
            "generated_templates": {},
            "errors": []
        }

        # Generate templates for each stage
        for stage in product_stages:
            try:
                # Skip template generation for Cool Off Period
                if stage == "Cool Off Period":
                    results["stage_results"][stage] = {
                        "success": True,
                        "templates_generated": 0,
                        "message": "Skipped - No emails sent during Cool Off Period"
                    }
                    continue

                # Get stage details
                stage_info = stage_details.get(stage, {
                    "current_stage": stage,
                    "description": f"User is in {stage} stage",
                    "goal_stage": "Next Stage"
                })

                # Generate templates for this stage
                stage_templates = generate_stage_templates(
                    stage=stage,
                    stage_details=stage_info,
                    num_templates=num_templates_per_stage,
                    product_data=product_data,
                    settings=settings
                )

                # Save templates
                save_success = save_templates(stage_templates, stage)

                if save_success:
                    results["stage_results"][stage] = {
                        "success": True,
                        "templates_generated": len(stage_templates),
                        "message": f"Successfully generated {len(stage_templates)} templates"
                    }
                    results["generated_templates"][stage] = stage_templates
                    results["total_templates"] += len(stage_templates)
                else:
                    results["stage_results"][stage] = {
                        "success": False,
                        "templates_generated": 0,
                        "message": "Failed to save templates"
                    }
                    results["errors"].append(f"Failed to save templates for stage: {stage}")

            except Exception as stage_error:
                results["stage_results"][stage] = {
                    "success": False,
                    "templates_generated": 0,
                    "message": f"Error: {str(stage_error)}"
                }
                results["errors"].append(f"Error generating templates for stage {stage}: {str(stage_error)}")
                results["success"] = False

        return results

    except Exception as e:
        return {
            "success": False,
            "total_templates": 0,
            "stage_results": {},
            "generated_templates": {},
            "errors": [f"Failed to create templates: {str(e)}"]
        }


# Utility functions
def get_stage_template(stage: str, product_name: str, organization_url: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get template for a specific stage and product.

    Args:
        stage (str): The journey stage
        product_name (str): The product name to match
        organization_url (str, optional): Organization URL to filter by

    Returns:
        Optional[Dict[str, Any]]: Template data if found, None otherwise
    """
    templates = load_templates(stage, organization_url, product_name)

    if templates:
        return templates[-1]  # Use most recent template

    return None


def get_all_stage_names() -> List[str]:
    """
    Get all available stage names from template files.

    Returns:
        List[str]: List of stage names
    """
    try:
        template_dir = 'data/templates'
        if not os.path.exists(template_dir):
            return []

        stage_names = []
        for filename in os.listdir(template_dir):
            if filename.endswith('.json'):
                stage_name = filename.replace('.json', '').replace('_', ' ').title()
                stage_names.append(stage_name)

        return sorted(stage_names)

    except Exception:
        return []


def validate_template_data(template_data: Dict[str, Any]) -> bool:
    """
    Validate template data structure.

    Args:
        template_data (Dict[str, Any]): Template data to validate

    Returns:
        bool: True if valid, False otherwise
    """
    required_fields = ["template_name", "template", "product_data", "settings", "stage"]

    for field in required_fields:
        if field not in template_data:
            return False

    # Validate template structure
    template = template_data.get("template", {})
    if not isinstance(template, dict) or "subject" not in template or "body" not in template:
        return False

    return True


def delete_templates(stage: str, template_names: List[str]) -> bool:
    """
    Delete specific templates from a stage.

    Args:
        stage (str): Stage name
        template_names (List[str]): List of template names to delete

    Returns:
        bool: True if deletion successful, False otherwise
    """
    try:
        stage_file = f'data/templates/{stage.lower().replace(" ", "_")}.json'

        if not os.path.exists(stage_file):
            return False

        with open(stage_file, 'r') as f:
            templates = json.load(f)

        # Filter out templates to delete
        updated_templates = [
            t for t in templates
            if t.get('template_name', '') not in template_names
        ]

        # Save updated templates
        with open(stage_file, 'w') as f:
            json.dump(updated_templates, f, indent=2)

        return True

    except Exception as e:
        print(f"Error deleting templates: {str(e)}")
        return False


def get_template_statistics(organization_url: Optional[str] = None) -> Dict[str, Any]:
    """
    Get statistics about generated templates.

    Args:
        organization_url (str, optional): Filter by organization URL

    Returns:
        Dict[str, Any]: Template statistics
    """
    try:
        stats = {
            "total_templates": 0,
            "stages": {},
            "products": {},
            "emoji_usage": {
                "subject_only": 0,
                "body_only": 0,
                "both": 0,
                "none": 0
            }
        }

        # Get all stage names
        stage_names = get_all_stage_names()

        for stage in stage_names:
            templates = load_templates(stage, organization_url)

            if templates:
                stats["stages"][stage] = len(templates)
                stats["total_templates"] += len(templates)

                for template in templates:
                    # Product statistics
                    product_name = template.get('product_data', {}).get('Product_Name', 'Unknown')
                    if product_name not in stats["products"]:
                        stats["products"][product_name] = 0
                    stats["products"][product_name] += 1

                    # Emoji usage statistics
                    has_emojis_subject = template.get('has_emojis_subject', False)
                    has_emojis_body = template.get('has_emojis_body', False)

                    if has_emojis_subject and has_emojis_body:
                        stats["emoji_usage"]["both"] += 1
                    elif has_emojis_subject:
                        stats["emoji_usage"]["subject_only"] += 1
                    elif has_emojis_body:
                        stats["emoji_usage"]["body_only"] += 1
                    else:
                        stats["emoji_usage"]["none"] += 1

        return stats

    except Exception as e:
        return {
            "total_templates": 0,
            "stages": {},
            "products": {},
            "emoji_usage": {"subject_only": 0, "body_only": 0, "both": 0, "none": 0},
            "error": str(e)
        }


# Example usage and testing function
def test_template_creation(test_product_data: Optional[Dict[str, Any]] = None) -> None:
    """
    Test function to demonstrate template creation.

    Args:
        test_product_data (Dict[str, Any], optional): Product data for testing
    """
    # Default test product data
    if not test_product_data:
        test_product_data = {
            "Product_Name": "GenAI Pinnacle Plus Program",
            "Company_Name": "Analytics Vidhya",
            "Product_URL": "https://www.analyticsvidhya.com/pinnacleplus/",
            "Type_of_Product": "Educational Program",
            "Product_Features": ["300+ Hours of Learning", "1:1 Mentorship", "Industry Projects"],
            "Product_Summary": "Comprehensive AI education program for professionals",
            "Priority": 1,
            "Company_URL": "https://www.analyticsvidhya.com/"
        }

    print(f"Testing template creation for: {test_product_data['Product_Name']}")
    print("-" * 50)

    # Test parameters
    product_stages = ["New Visitor", "Product Page Viewed", "Product Lead Generated"]
    stage_details = {
        "New Visitor": {
            "current_stage": "New Visitor",
            "description": "User is not aware of the product",
            "goal_stage": "Product Page Viewed"
        },
        "Product Page Viewed": {
            "current_stage": "Product Page Viewed",
            "description": "User has viewed product details",
            "goal_stage": "Product Lead Generated"
        },
        "Product Lead Generated": {
            "current_stage": "Product Lead Generated",
            "description": "User has shown interest in the product",
            "goal_stage": "Product Purchased"
        }
    }
    num_templates_per_stage = 2
    include_emojis = {
        "use_emojis_subject": True,
        "use_emojis_body": False
    }

    # Create templates
    results = create_templates(
        product_stages=product_stages,
        stage_details=stage_details,
        num_templates_per_stage=num_templates_per_stage,
        include_emojis=include_emojis,
        product_data=test_product_data,
        organization_url=test_product_data.get('Company_URL')
    )

    # Display results
    print("Template Creation Results:")
    print(f"Success: {results['success']}")
    print(f"Total Templates Generated: {results['total_templates']}")

    for stage, stage_result in results['stage_results'].items():
        print(f"\n{stage}:")
        print(f"  Success: {stage_result['success']}")
        print(f"  Templates: {stage_result['templates_generated']}")
        print(f"  Message: {stage_result['message']}")

    if results['errors']:
        print(f"\nErrors: {results['errors']}")

    # Display template statistics
    stats = get_template_statistics(test_product_data.get('Company_URL'))
    print(f"\nTemplate Statistics:")
    print(f"Total Templates: {stats['total_templates']}")
    print(f"Stages: {list(stats['stages'].keys())}")
    print(f"Emoji Usage: {stats['emoji_usage']}")


if __name__ == "__main__":
    # Test the module
    test_template_creation()
