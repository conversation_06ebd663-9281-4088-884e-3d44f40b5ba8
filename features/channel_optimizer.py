"""
Channel Optimization Backend Script
Aggregates all channel selection and optimization functionality including channel selection algorithm,
user engagement analysis, batch processing, and campaign channel optimization.

This script combines functionality from:
- src/openengage/core/channel_selector.py
- src/openengage/core/channel_selection_util.py
- Related utility functions

Inputs:
- users_data: List of dictionaries or DataFrame containing user data with user_email and/or phone_number
- content_type: Type of content being sent ("general", "promotional", "educational", etc.)
- urgency_level: Level of urgency ("low", "medium", "high")
- performance_data_path: Optional path to performance data CSV for engagement metrics
- weights: Optional dictionary of weights for different factors in channel selection
- output_dir: Optional directory to save output CSV files

Outputs:
- Dictionary with users grouped by selected channel (email, whatsapp, unavailable)
- Channel selection scores and recommendations
- CSV files with segmented user data by channel
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

def setup_channel_optimizer_logging():
    """Setup comprehensive logging for channel optimizer module with file storage."""
    # Create logs directory if it doesn't exist
    logs_dir = 'data/logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)

    # Create a unique log file name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"channel_optimizer_{timestamp}.log"
    log_filepath = os.path.join(logs_dir, log_filename)

    # Configure logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler
    file_handler = logging.FileHandler(log_filepath)
    file_handler.setLevel(logging.DEBUG)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Setup logger
logger = setup_channel_optimizer_logging()

class ChannelOptimizer:
    """
    Channel optimization algorithm to determine the optimal channel (Email or WhatsApp) for each user.
    """
    
    # Channel types
    EMAIL = "email"
    WHATSAPP = "whatsapp"
    
    # Weights for different factors in channel selection
    DEFAULT_WEIGHTS = {
        "availability": 0.4,        # Weight for channel availability
        "engagement": 0.3,          # Weight for past engagement
        "content_suitability": 0.2, # Weight for content suitability
        "urgency": 0.1,             # Weight for message urgency
    }
    
    # Content type preferences for each channel
    CONTENT_PREFERENCES = {
        "promotional": {EMAIL: 0.8, WHATSAPP: 0.6},
        "educational": {EMAIL: 0.9, WHATSAPP: 0.5},
        "transactional": {EMAIL: 0.7, WHATSAPP: 0.9},
        "urgent": {EMAIL: 0.5, WHATSAPP: 0.9},
        "general": {EMAIL: 0.7, WHATSAPP: 0.7},
    }
    
    # Urgency level preferences for each channel
    URGENCY_PREFERENCES = {
        "low": {EMAIL: 0.8, WHATSAPP: 0.6},
        "medium": {EMAIL: 0.7, WHATSAPP: 0.7},
        "high": {EMAIL: 0.5, WHATSAPP: 0.9},
    }

    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        Initialize the ChannelOptimizer.
        
        Args:
            weights: Optional dictionary of weights for different factors
        """
        self.weights = weights or self.DEFAULT_WEIGHTS.copy()
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the optimizer"""
        logger = logging.getLogger("openengage.channel_optimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get_channel_availability(self, user_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Determine channel availability for the user.
        
        Args:
            user_data: Dictionary containing user data
        
        Returns:
            Dictionary with availability score for each channel (0-1)
        """
        availability = {
            self.EMAIL: 0.0,
            self.WHATSAPP: 0.0,
        }
        
        # Email is available if user_email exists and is not empty
        if user_data.get("user_email") and isinstance(user_data["user_email"], str) and "@" in user_data["user_email"]:
            availability[self.EMAIL] = 1.0
            
        # WhatsApp is available if phone_number exists and is not empty
        if user_data.get("phone_number") and isinstance(user_data["phone_number"], str) and len(user_data["phone_number"]) >= 10:
            availability[self.WHATSAPP] = 1.0
            
        return availability

    def get_channel_engagement(self, user_data: Dict[str, Any], engagement_data: Optional[Dict[str, Any]] = None) -> Dict[str, float]:
        """
        Get engagement scores for each channel based on historical data.
        
        Args:
            user_data: Dictionary containing user data
            engagement_data: Optional pre-computed engagement metrics
        
        Returns:
            Dictionary with engagement score for each channel (0-1)
        """
        engagement = {
            self.EMAIL: 0.5,     # Default engagement score
            self.WHATSAPP: 0.5,  # Default engagement score
        }
        
        if engagement_data:
            # Email engagement
            email = user_data.get("user_email")
            if email and email in engagement_data.get("email", {}):
                email_metrics = engagement_data["email"][email]
                # Combine open rate and click rate for engagement score
                open_rate = email_metrics.get("open_rate", 0) / 100
                click_rate = email_metrics.get("click_rate", 0) / 100
                engagement[self.EMAIL] = min(1.0, (open_rate * 0.7 + click_rate * 0.3))
                
            # WhatsApp engagement
            phone = user_data.get("phone_number")
            if phone and phone in engagement_data.get("whatsapp", {}):
                whatsapp_metrics = engagement_data["whatsapp"][phone]
                # Combine read rate and response rate for engagement score
                read_rate = whatsapp_metrics.get("read_rate", 0) / 100
                response_rate = whatsapp_metrics.get("response_rate", 0) / 100
                engagement[self.WHATSAPP] = min(1.0, (read_rate * 0.6 + response_rate * 0.4))
        
        return engagement

    def get_content_suitability(self, content_type: str) -> Dict[str, float]:
        """
        Get content suitability scores for each channel.
        
        Args:
            content_type: Type of content being sent
        
        Returns:
            Dictionary with suitability score for each channel (0-1)
        """
        return self.CONTENT_PREFERENCES.get(content_type.lower(), self.CONTENT_PREFERENCES["general"])

    def get_urgency_factor(self, urgency_level: str) -> Dict[str, float]:
        """
        Get urgency factor scores for each channel.
        
        Args:
            urgency_level: Level of urgency
        
        Returns:
            Dictionary with urgency score for each channel (0-1)
        """
        return self.URGENCY_PREFERENCES.get(urgency_level.lower(), self.URGENCY_PREFERENCES["medium"])

    def select_channel(self, user_data: Dict[str, Any], 
                       content_type: str = "general", 
                       urgency_level: str = "medium",
                       engagement_data: Optional[Dict[str, Any]] = None) -> Tuple[str, Dict[str, float]]:
        """
        Select the optimal channel for the user based on various factors.
        
        Args:
            user_data: Dictionary containing user data (must include user_email and/or phone_number)
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")
            engagement_data: Optional dictionary containing pre-computed engagement metrics
            
        Returns:
            Tuple of (selected_channel, scores_dict)
        """
        # Get scores for different factors
        availability = self.get_channel_availability(user_data)
        engagement = self.get_channel_engagement(user_data, engagement_data)
        content_suitability = self.get_content_suitability(content_type)
        urgency = self.get_urgency_factor(urgency_level)
        
        # Calculate weighted scores for each channel
        scores = {}
        for channel in [self.EMAIL, self.WHATSAPP]:
            # If channel is not available, score is 0
            if availability[channel] == 0:
                scores[channel] = 0
                continue
                
            # Calculate weighted score
            score = (
                self.weights["availability"] * availability[channel] +
                self.weights["engagement"] * engagement[channel] +
                self.weights["content_suitability"] * content_suitability[channel] +
                self.weights["urgency"] * urgency[channel]
            )
            scores[channel] = score
        
        # Get the channel with the highest score
        if scores[self.EMAIL] >= scores[self.WHATSAPP]:
            selected_channel = self.EMAIL
        else:
            selected_channel = self.WHATSAPP
            
        # If selected channel has no availability, use the alternative channel if available
        if availability[selected_channel] == 0:
            alternative = self.WHATSAPP if selected_channel == self.EMAIL else self.EMAIL
            if availability[alternative] > 0:
                selected_channel = alternative
            else:
                # If neither channel is available, default to email (for logging)
                selected_channel = self.EMAIL
                self.logger.warning(f"No valid channel found for user: {user_data.get('user_email', 'unknown')}")
        
        # Return selected channel and scores
        return selected_channel, scores

    def batch_select_channels(self, users_data: List[Dict[str, Any]],
                             content_type: str = "general",
                             urgency_level: str = "medium") -> Dict[str, List[Dict[str, Any]]]:
        """
        Select channels for a batch of users and group them by selected channel.

        Args:
            users_data: List of dictionaries containing user data
            content_type: Type of content being sent
            urgency_level: Level of urgency ("low", "medium", "high")

        Returns:
            Dictionary with users grouped by selected channel
        """
        # Calculate engagement data for all users first
        engagement_data = self._batch_calculate_engagement(users_data)

        # Group users by channel
        channels = {
            self.EMAIL: [],
            self.WHATSAPP: [],
            "unavailable": []
        }

        # Process each user
        for user_data in users_data:
            # Skip if neither email nor phone number exists
            if not user_data.get("user_email") and not user_data.get("phone_number"):
                channels["unavailable"].append(user_data)
                continue

            # Select channel for user
            selected_channel, _ = self.select_channel(
                user_data,
                content_type=content_type,
                urgency_level=urgency_level,
                engagement_data=engagement_data
            )

            # Add user to the appropriate channel group
            if selected_channel in channels:
                channels[selected_channel].append(user_data)

        return channels

    def _batch_calculate_engagement(self, users_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        Calculate engagement data for a batch of users.

        Args:
            users_data: List of user data dictionaries

        Returns:
            Dictionary with engagement data for email and whatsapp channels
        """
        engagement_data = {
            "email": {},
            "whatsapp": {}
        }

        # Generate engagement metrics for each user
        for user_data in users_data:
            # Email engagement
            if user_data.get("user_email"):
                email = user_data["user_email"]
                # Generate some random engagement metrics
                engagement_data["email"][email] = {
                    "open_rate": np.random.uniform(15, 45),     # Random open rate between 15% and 45%
                    "click_rate": np.random.uniform(2, 15),     # Random click rate between 2% and 15%
                }

            # WhatsApp engagement
            if user_data.get("phone_number"):
                phone = user_data["phone_number"]
                # Generate some random engagement metrics
                engagement_data["whatsapp"][phone] = {
                    "read_rate": np.random.uniform(50, 90),     # Random read rate between 50% and 90%
                    "response_rate": np.random.uniform(5, 40),  # Random response rate between 5% and 40%
                }

        return engagement_data


def load_user_engagement_from_performance_data(performance_file_path: str) -> Dict[str, Dict[str, Dict[str, float]]]:
    """
    Load user engagement data from the performance CSV file.

    Args:
        performance_file_path: Path to the performance CSV file

    Returns:
        Dictionary with engagement data for email and whatsapp channels
    """
    engagement_data = {
        "email": {},
        "whatsapp": {}
    }

    try:
        if not os.path.exists(performance_file_path):
            logger.warning(f"Performance file not found: {performance_file_path}")
            return engagement_data

        df = pd.read_csv(performance_file_path)

        # Group by user_email and calculate engagement metrics
        if 'user_email' in df.columns:
            email_metrics = df.groupby('user_email').agg({
                'Open_Time': lambda x: (x.notna().sum() / len(x)) * 100,  # Open rate
                'Click_Time': lambda x: (x.notna().sum() / len(x)) * 100,  # Click rate
            }).reset_index()

            # Convert to dictionary format
            for _, row in email_metrics.iterrows():
                email = row['user_email']
                engagement_data["email"][email] = {
                    "open_rate": row['Open_Time'],
                    "click_rate": row['Click_Time']
                }

        # Note: WhatsApp engagement data would need to be loaded from a separate source
        # as it's typically not in the same performance file as email data

    except Exception as e:
        logger.error(f"Error loading engagement data from {performance_file_path}: {str(e)}")

    return engagement_data


def select_channels_for_campaign(
    users_data_path: str,
    content_type: str = "general",
    urgency_level: str = "medium",
    performance_data_path: Optional[str] = None,
    output_dir: Optional[str] = None,
    weights: Optional[Dict[str, float]] = None
) -> Dict[str, pd.DataFrame]:
    """
    Selects the optimal channel for each user in a campaign and outputs segmented CSVs.

    Args:
        users_data_path: Path to the CSV file with user data
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        output_dir: Directory to save output CSV files (default: current directory)
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with DataFrames for each channel (email, whatsapp, unavailable)
    """
    try:
        # Load user data
        if not os.path.exists(users_data_path):
            logger.error(f"User data file not found: {users_data_path}")
            return {}

        users_df = pd.read_csv(users_data_path)
        logger.info(f"Loaded {len(users_df)} users from {users_data_path}")

        # Validate required columns
        if 'user_email' not in users_df.columns:
            logger.error("'user_email' column not found in user data")
            return {}

        # Convert to list of dictionaries
        users_data = users_df.to_dict('records')

        # Initialize channel optimizer with custom weights if provided
        channel_optimizer = ChannelOptimizer(weights=weights)

        # Load engagement data if performance file provided
        engagement_data = None
        if performance_data_path and os.path.exists(performance_data_path):
            engagement_data = load_user_engagement_from_performance_data(performance_data_path)

        # Process channels
        logger.info(f"Processing channels for {len(users_data)} users")
        channels_dict = channel_optimizer.batch_select_channels(
            users_data,
            content_type=content_type,
            urgency_level=urgency_level
        )

        # Create result dataframes
        result = {}

        # Email users
        email_users = channels_dict.get("email", [])
        if email_users:
            # Get user IDs for filtering
            email_ids = [u.get("user_email") for u in email_users if u.get("user_email")]
            result["email"] = users_df[users_df["user_email"].isin(email_ids)]
        else:
            result["email"] = pd.DataFrame()

        # WhatsApp users
        whatsapp_users = channels_dict.get("whatsapp", [])
        if whatsapp_users and "phone_number" in users_df.columns:
            # Get user phone numbers for filtering
            phone_numbers = [u.get("phone_number") for u in whatsapp_users if u.get("phone_number")]
            result["whatsapp"] = users_df[users_df["phone_number"].isin(phone_numbers)]
        else:
            result["whatsapp"] = pd.DataFrame()

        # Unavailable users
        unavailable_users = channels_dict.get("unavailable", [])
        if unavailable_users:
            # Identify unavailable users (those with neither email nor phone)
            if "phone_number" in users_df.columns:
                result["unavailable"] = users_df[
                    (~users_df["user_email"].isin([u.get("user_email") for u in email_users if u.get("user_email")])) &
                    (~users_df["phone_number"].isin([u.get("phone_number") for u in whatsapp_users if u.get("phone_number")]))
                ]
            else:
                result["unavailable"] = users_df[
                    ~users_df["user_email"].isin([u.get("user_email") for u in email_users if u.get("user_email")])
                ]
        else:
            result["unavailable"] = pd.DataFrame()

        # Save CSV files if output directory provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save email users
            if not result["email"].empty:
                email_path = os.path.join(output_dir, f"email_users_{timestamp}.csv")
                result["email"].to_csv(email_path, index=False)
                logger.info(f"Saved {len(result['email'])} email users to {email_path}")

            # Save WhatsApp users
            if not result["whatsapp"].empty:
                whatsapp_path = os.path.join(output_dir, f"whatsapp_users_{timestamp}.csv")
                result["whatsapp"].to_csv(whatsapp_path, index=False)
                logger.info(f"Saved {len(result['whatsapp'])} WhatsApp users to {whatsapp_path}")

            # Save unavailable users
            if not result["unavailable"].empty:
                unavailable_path = os.path.join(output_dir, f"unavailable_users_{timestamp}.csv")
                result["unavailable"].to_csv(unavailable_path, index=False)
                logger.info(f"Saved {len(result['unavailable'])} unavailable users to {unavailable_path}")

        return result

    except Exception as e:
        logger.error(f"Error in channel selection: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {}


def get_user_channel_with_scores(
    user_data: Dict[str, Any],
    content_type: str = "general",
    urgency_level: str = "medium",
    performance_data_path: Optional[str] = None,
    weights: Optional[Dict[str, float]] = None
) -> Dict[str, Any]:
    """
    Get the recommended channel and scores for a single user.

    Args:
        user_data: Dictionary containing user data (must include user_email and/or phone_number)
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with selected channel and scores
    """
    try:
        # Initialize channel optimizer with custom weights if provided
        channel_optimizer = ChannelOptimizer(weights=weights)

        # Load engagement data if performance file provided
        engagement_data = None
        if performance_data_path and os.path.exists(performance_data_path):
            engagement_data = load_user_engagement_from_performance_data(performance_data_path)

        # Select channel for user
        channel, scores = channel_optimizer.select_channel(
            user_data,
            content_type=content_type,
            urgency_level=urgency_level,
            engagement_data=engagement_data
        )

        # Return result
        result = {
            "selected_channel": channel,
            "email_score": scores.get("email", 0),
            "whatsapp_score": scores.get("whatsapp", 0),
        }

        # Add user identifiers if they exist
        if "user_email" in user_data:
            result["user_email"] = user_data["user_email"]
        if "phone_number" in user_data:
            result["phone_number"] = user_data["phone_number"]

        return result

    except Exception as e:
        logger.error(f"Error getting user channel: {str(e)}")
        return {"selected_channel": "email", "email_score": 0, "whatsapp_score": 0}


# API Functions for external use

def optimize_channels_for_campaign(users_data_path: str, content_type: str = "general",
                                 urgency_level: str = "medium", performance_data_path: str = None,
                                 output_dir: str = None, weights: Dict[str, float] = None) -> Dict[str, pd.DataFrame]:
    """
    Optimize channel selection for a campaign and return segmented user data.

    Args:
        users_data_path: Path to the CSV file with user data
        content_type: Type of content being sent
        urgency_level: Level of urgency ("low", "medium", "high")
        performance_data_path: Optional path to performance data CSV
        output_dir: Optional directory to save output CSV files
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with DataFrames for each channel
    """
    return select_channels_for_campaign(
        users_data_path=users_data_path,
        content_type=content_type,
        urgency_level=urgency_level,
        performance_data_path=performance_data_path,
        output_dir=output_dir,
        weights=weights
    )


def get_optimal_channel_for_user(user_data: Dict[str, Any], content_type: str = "general",
                                urgency_level: str = "medium", performance_data_path: str = None,
                                weights: Dict[str, float] = None) -> Dict[str, Any]:
    """
    Get the optimal channel recommendation for a single user.

    Args:
        user_data: Dictionary containing user data
        content_type: Type of content being sent
        urgency_level: Level of urgency
        performance_data_path: Optional path to performance data CSV
        weights: Optional dictionary of weights for different factors

    Returns:
        Dictionary with channel recommendation and scores
    """
    return get_user_channel_with_scores(
        user_data=user_data,
        content_type=content_type,
        urgency_level=urgency_level,
        performance_data_path=performance_data_path,
        weights=weights
    )


def load_engagement_metrics(performance_data_path: str) -> Dict[str, Dict[str, Dict[str, float]]]:
    """
    Load user engagement metrics from performance data.

    Args:
        performance_data_path: Path to performance data CSV

    Returns:
        Dictionary containing engagement metrics for email and whatsapp channels
    """
    return load_user_engagement_from_performance_data(performance_data_path)


if __name__ == "__main__":
    # Example usage
    sample_user_data = {
        "user_email": "<EMAIL>",
        "phone_number": "+1234567890",
        "first_name": "John"
    }

    # Get channel recommendation for single user
    recommendation = get_optimal_channel_for_user(
        user_data=sample_user_data,
        content_type="promotional",
        urgency_level="medium"
    )

    print("Channel Optimization Result:")
    print(f"Selected Channel: {recommendation['selected_channel']}")
    print(f"Email Score: {recommendation['email_score']:.3f}")
    print(f"WhatsApp Score: {recommendation['whatsapp_score']:.3f}")

    # Example batch processing (uncomment to test with actual CSV file)
    # results = optimize_channels_for_campaign(
    #     users_data_path="sample_users.csv",
    #     content_type="promotional",
    #     urgency_level="medium",
    #     output_dir="channel_results"
    # )
    #
    # print(f"\nBatch Processing Results:")
    # print(f"Email users: {len(results.get('email', []))}")
    # print(f"WhatsApp users: {len(results.get('whatsapp', []))}")
    # print(f"Unavailable users: {len(results.get('unavailable', []))}")
