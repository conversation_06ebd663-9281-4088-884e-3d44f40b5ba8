#!/usr/bin/env python3
"""
Test script to verify that API changes work correctly.
This script tests that the APIs work without redundant saving.
"""

import sys
import os
import json

# Add Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

def test_backend_functions():
    """Test that backend functions work without automatic saving."""
    print("🧪 Testing Backend Functions (without automatic saving)")
    print("=" * 60)
    
    try:
        # Test organization data
        from org_data import get_organization_data
        print("✅ Testing organization data...")
        org_result = get_organization_data('https://www.analyticsvidhya.com/')
        print(f"   Domain: {org_result.get('Domain', 'N/A')}")
        print(f"   Class: {org_result.get('Class', 'N/A')}")
        print("   ✅ Organization data function works!")
        
        # Test brand archetype (this might take longer due to OpenAI API)
        print("\n✅ Testing brand archetype...")
        from archetype import get_brand_archetype_analysis
        archetype_result = get_brand_archetype_analysis('https://www.analyticsvidhya.com/')
        print(f"   Primary Archetype: {archetype_result.get('primary_archetype', 'N/A')}")
        print("   ✅ Brand archetype function works!")
        
        # Test brand guidelines
        print("\n✅ Testing brand guidelines...")
        from brand_guidelines import get_brand_guidelines
        guidelines_result = get_brand_guidelines('https://www.analyticsvidhya.com/')
        print(f"   Primary Color: {guidelines_result.get('primary_color', 'N/A')}")
        print("   ✅ Brand guidelines function works!")
        
        print("\n🎉 All backend functions work correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing backend functions: {str(e)}")
        return False

def test_api_imports():
    """Test that API routes import correctly."""
    print("\n🧪 Testing API Routes Import")
    print("=" * 60)
    
    try:
        import core.api.routes
        print("✅ API routes imported successfully!")
        
        # Check that save functions are not imported
        import inspect
        routes_module = core.api.routes
        
        # Get all imported names
        imported_names = [name for name, obj in inspect.getmembers(routes_module) 
                         if not name.startswith('_')]
        
        # Check that save functions are not imported
        save_functions = ['save_organization_data', 'save_archetype_data', 
                         'save_brand_data', 'save_product_data']
        
        found_save_functions = [func for func in save_functions if func in imported_names]
        
        if found_save_functions:
            print(f"⚠️  Warning: Found save functions still imported: {found_save_functions}")
        else:
            print("✅ Save functions correctly removed from imports!")
            
        return True
        
    except Exception as e:
        print(f"❌ Error importing API routes: {str(e)}")
        return False

def check_data_directory():
    """Check what's in the data directory."""
    print("\n🧪 Checking Data Directory")
    print("=" * 60)
    
    data_dir = "data"
    if os.path.exists(data_dir):
        files = os.listdir(data_dir)
        print(f"📁 Data directory contains: {files}")
        
        # Check organization data
        org_file = os.path.join(data_dir, "organization_data.json")
        if os.path.exists(org_file):
            with open(org_file, 'r') as f:
                org_data = json.load(f)
            print(f"📄 Organization data has {len(org_data)} entries")
            
        return True
    else:
        print("📁 No data directory found")
        return False

def main():
    """Main test function."""
    print("🚀 Testing API Changes - No Redundant Saving")
    print("=" * 80)
    
    # Test 1: API imports
    api_test = test_api_imports()
    
    # Test 2: Check data directory
    data_test = check_data_directory()
    
    # Test 3: Backend functions (this will take longer)
    print("\n⏳ Note: Backend function tests may take 1-2 minutes due to OpenAI API calls...")
    backend_test = test_backend_functions()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ API Imports: {'PASS' if api_test else 'FAIL'}")
    print(f"✅ Data Directory: {'PASS' if data_test else 'FAIL'}")
    print(f"✅ Backend Functions: {'PASS' if backend_test else 'FAIL'}")
    
    if all([api_test, backend_test]):
        print("\n🎉 ALL TESTS PASSED! API changes are working correctly.")
        print("💡 The APIs now work without redundant saving - data is only saved when explicitly requested through the API endpoints.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
