"""
Performance Prediction Backend Script
Aggregates all campaign performance prediction and analytics functionality including performance analysis,
prediction models, metrics calculation, and visualization data preparation.

This script combines functionality from:
- src/openengage/core/campaign_predictor.py
- src/openengage/core/mail_performance_analyzer.py
- src/openengage/analytics.py
- Related utility functions

Inputs:
- campaign_file: Path to campaign data CSV file
- performance_file: Path to historical performance data CSV file
- campaign_data: DataFrame or dict containing campaign information
- user_data: User engagement and behavior data
- model_config: Optional configuration for prediction models

Outputs:
- Predicted performance metrics (open_rate, click_rate, unsub_rate)
- Performance analysis results and visualizations
- Model training metrics and accuracy scores
- Campaign optimization recommendations
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from glob import glob
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.metrics import mean_squared_error, r2_score

def setup_performance_predictor_logging():
    """Setup comprehensive logging for performance predictor module with file storage."""
    # Create logs directory if it doesn't exist
    logs_dir = 'data/logs'
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)

    # Create a unique log file name with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"performance_predictor_{timestamp}.log"
    log_filepath = os.path.join(logs_dir, log_filename)

    # Configure logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create file handler
    file_handler = logging.FileHandler(log_filepath)
    file_handler.setLevel(logging.DEBUG)

    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Setup logger
logger = setup_performance_predictor_logging()

class PerformancePredictor:
    """
    A class to predict campaign performance metrics based on historical data.
    """
    
    def __init__(self):
        """Initialize the PerformancePredictor."""
        self.models = {
            'open_rate': None,
            'click_rate': None,
            'unsub_rate': None
        }
        self.feature_columns = None
        self.preprocessor = None
        self.model_trained = False
        self.training_metrics = {}
        self.logger = self._setup_logger()

    def _setup_logger(self):
        """Set up logger for the predictor"""
        logger = logging.getLogger("openengage.performance_predictor")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _extract_features_from_campaign(self, campaign_df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract features from campaign data for prediction.
        
        Args:
            campaign_df: DataFrame containing campaign data
            
        Returns:
            DataFrame with extracted features
        """
        features = pd.DataFrame()
        
        # Time-based features
        if 'Send_Time' in campaign_df.columns:
            campaign_df['Send_Time'] = pd.to_datetime(campaign_df['Send_Time'])
            features['hour_of_day'] = campaign_df['Send_Time'].dt.hour
            features['day_of_week'] = campaign_df['Send_Time'].dt.dayofweek
            features['is_weekend'] = (campaign_df['Send_Time'].dt.dayofweek >= 5).astype(int)
        else:
            # Default values if Send_Time is not available
            features['hour_of_day'] = 10  # Default to 10 AM
            features['day_of_week'] = 2   # Default to Tuesday
            features['is_weekend'] = 0    # Default to weekday
        
        # Subject line features
        if 'Subject' in campaign_df.columns:
            features['subject_length'] = campaign_df['Subject'].str.len().fillna(0)
            features['subject_word_count'] = campaign_df['Subject'].str.split().str.len().fillna(0)
            features['has_emoji'] = campaign_df['Subject'].str.contains(r'[^\w\s]', regex=True, na=False).astype(int)
        else:
            features['subject_length'] = 50      # Default subject length
            features['subject_word_count'] = 8   # Default word count
            features['has_emoji'] = 0            # Default no emoji
        
        # Content features
        if 'Mail_Content' in campaign_df.columns:
            features['content_length'] = campaign_df['Mail_Content'].str.len().fillna(0)
            features['content_word_count'] = campaign_df['Mail_Content'].str.split().str.len().fillna(0)
        else:
            features['content_length'] = 500     # Default content length
            features['content_word_count'] = 100 # Default word count
        
        # User behavior features
        if 'user_behaviour' in campaign_df.columns:
            features['user_behavior_length'] = campaign_df['user_behaviour'].str.len().fillna(0)
        else:
            features['user_behavior_length'] = 100  # Default behavior length
        
        # Campaign type features
        if 'user_stage' in campaign_df.columns:
            # Encode user stages
            stage_encoder = LabelEncoder()
            features['user_stage_encoded'] = stage_encoder.fit_transform(campaign_df['user_stage'].fillna('unknown'))
        else:
            features['user_stage_encoded'] = 0  # Default stage
        
        # Fill any remaining NaN values
        features = features.fillna(0)
        
        return features

    def _prepare_training_data(self, performance_df):
        """
        Prepare training data from performance data.
        
        Args:
            performance_df (pd.DataFrame): DataFrame containing performance data
            
        Returns:
            tuple: X (features) and y (targets) for training
        """
        # Extract features
        features = self._extract_features_from_campaign(performance_df)
        
        # Prepare target variables
        targets = pd.DataFrame()
        
        # Calculate open rate
        if 'Open_Time' in performance_df.columns:
            targets['open_rate'] = performance_df['Open_Time'].notna().astype(int)
            
        # Calculate click rate
        if 'Click_Time' in performance_df.columns:
            targets['click_rate'] = performance_df['Click_Time'].notna().astype(int)
            
        # Calculate unsubscribe rate
        if 'Unsub_Time' in performance_df.columns:
            targets['unsub_rate'] = performance_df['Unsub_Time'].notna().astype(int)
        
        # Store feature columns for later use
        self.feature_columns = features.columns.tolist()
        
        # Create preprocessor for features
        numeric_features = features.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = features.select_dtypes(include=['object']).columns.tolist()
        
        transformers = []
        if numeric_features:
            transformers.append(('num', StandardScaler(), numeric_features))
        if categorical_features:
            transformers.append(('cat', LabelEncoder(), categorical_features))
            
        self.preprocessor = ColumnTransformer(transformers)
        
        # Preprocess features
        X = self.preprocessor.fit_transform(features)
        
        return X, targets

    def train_models(self, performance_file=None):
        """
        Train prediction models using historical performance data.
        
        Args:
            performance_file (str, optional): Path to the performance data file.
                If None, will use the latest file in data/mail_performance/combined/.
                
        Returns:
            bool: True if training was successful, False otherwise
        """
        try:
            # Find the performance file if not provided
            if performance_file is None:
                performance_files = glob("data/mail_performance/combined/all_performance_*.csv")
                if not performance_files:
                    logger.warning("No performance data files found")
                    return False
                performance_file = max(performance_files)  # Get the most recent file
                
            logger.info(f"Training models using performance data from: {performance_file}")
            
            # Load performance data
            performance_df = pd.read_csv(performance_file)
            
            # Check if we have enough data for training
            if len(performance_df) < 50:
                logger.warning(f"Not enough data for training ({len(performance_df)} records). Need at least 50.")
                return False
            
            # Prepare training data
            X, targets = self._prepare_training_data(performance_df)
            
            # Train models for each target
            for target in ['open_rate', 'click_rate', 'unsub_rate']:
                if target in targets.columns:
                    # Split data
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, targets[target], test_size=0.2, random_state=42
                    )
                    
                    # Create and train model
                    model = RandomForestRegressor(n_estimators=100, random_state=42)
                    model.fit(X_train, y_train)
                    
                    # Evaluate model
                    y_pred = model.predict(X_test)
                    mse = mean_squared_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                    
                    # Store model and metrics
                    self.models[target] = model
                    self.training_metrics[target] = {
                        'mse': mse,
                        'r2': r2,
                        'baseline': targets[target].mean()
                    }
                    
                    logger.info(f"Trained {target} model. MSE: {mse:.4f}, R²: {r2:.4f}")
                else:
                    logger.warning(f"Target '{target}' not found in performance data")
            
            self.model_trained = True
            
            # Save model metadata
            self._save_model_metadata()
            
            return True
            
        except Exception as e:
            logger.error(f"Error training models: {str(e)}")
            return False

    def _save_model_metadata(self):
        """Save model metadata to a JSON file."""
        metadata = {
            'trained_at': datetime.now().isoformat(),
            'feature_columns': self.feature_columns,
            'metrics': self.training_metrics
        }
        
        os.makedirs("data/models", exist_ok=True)
        with open("data/models/campaign_predictor_metadata.json", "w") as f:
            json.dump(metadata, f, indent=2)
            
        logger.info("Saved model metadata to data/models/campaign_predictor_metadata.json")

    def predict_campaign_performance(self, campaign_file=None):
        """
        Predict performance metrics for a campaign.

        Args:
            campaign_file (str, optional): Path to the campaign data file.
                If None, will use the latest file in data/campaign_results/.

        Returns:
            dict: Predicted performance metrics
        """
        try:
            # Check if models are trained
            if not self.model_trained:
                logger.warning("Models are not trained. Training now...")
                if not self.train_models():
                    return {
                        'success': False,
                        'error': 'Failed to train models'
                    }

            # Find the campaign file if not provided
            if campaign_file is None:
                campaign_files = glob("data/campaign_results/*.csv")
                if not campaign_files:
                    logger.warning("No campaign files found")
                    return {
                        'success': False,
                        'error': 'No campaign files found'
                    }
                campaign_file = max(campaign_files)  # Get the most recent file

            logger.info(f"Predicting performance for campaign: {campaign_file}")

            # Load campaign data
            campaign_df = pd.read_csv(campaign_file)

            # Extract features
            features = self._extract_features_from_campaign(campaign_df)

            # Check if we have the necessary features
            missing_features = [col for col in self.feature_columns if col not in features.columns]
            if missing_features:
                logger.warning(f"Missing features: {missing_features}")
                # Add missing features with default values
                for col in missing_features:
                    features[col] = 0

            # Ensure features are in the same order as during training
            features = features[self.feature_columns]

            # Preprocess features
            X = self.preprocessor.transform(features)

            # Make predictions
            predictions = {}
            for target, model in self.models.items():
                if model is not None:
                    # Predict for each row
                    row_predictions = model.predict(X)

                    # Calculate average prediction
                    avg_prediction = np.mean(row_predictions) * 100  # Convert to percentage

                    predictions[target] = avg_prediction

                    logger.info(f"Predicted {target}: {avg_prediction:.2f}%")
                else:
                    logger.warning(f"No model available for {target}")

            # Add metadata
            predictions.update({
                'success': True,
                'campaign_file': campaign_file,
                'prediction_date': datetime.now().isoformat(),
                'total_recipients': len(campaign_df)
            })

            return predictions

        except Exception as e:
            logger.error(f"Error predicting campaign performance: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }


class PerformanceAnalyzer:
    """
    A class to analyze campaign performance and generate insights.
    """

    def __init__(self):
        """Initialize the PerformanceAnalyzer."""
        self.logger = self._setup_logger()
        self.year_month = datetime.now().strftime("%Y_%m")

    def _setup_logger(self):
        """Set up logger for the analyzer"""
        logger = logging.getLogger("openengage.performance_analyzer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def calculate_performance_metrics(self, performance_file=None):
        """
        Calculate comprehensive performance metrics from performance data.

        Args:
            performance_file (str, optional): Path to the performance data file.
                If None, will use the latest combined performance file.

        Returns:
            dict: Performance metrics and visualization data
        """
        # Find performance file if not provided
        if performance_file is None:
            # Try to find the latest combined performance file
            performance_file = Path(f"data/mail_performance/combined/all_performance_{self.year_month}.csv")

            if not performance_file.exists():
                # Fall back to the monthly performance file
                performance_file = f"data/mail_performance/mail_performance_{self.year_month}.csv"

                if not os.path.exists(performance_file):
                    # Try to find any performance file
                    performance_files = glob("data/mail_performance/mail_performance_*.csv")
                    if performance_files:
                        performance_file = max(performance_files)  # Get the most recent file
                    else:
                        logger.warning("No performance data files found")
                        return {
                            "total_emails": 0,
                            "open_count": 0,
                            "open_rate": 0,
                            "click_count": 0,
                            "click_rate": 0,
                            "click_to_open_rate": 0,
                            "unsub_count": 0,
                            "unsub_rate": 0
                        }

        try:
            df = pd.read_csv(performance_file)

            # Calculate metrics
            total_emails = len(df)

            # Check if required columns exist
            open_count = 0
            if 'Open_Time' in df.columns:
                open_count = df['Open_Time'].notna().sum()
            else:
                logger.warning("'Open_Time' column not found in performance data")

            open_rate = (open_count / total_emails) * 100 if total_emails > 0 else 0

            click_count = 0
            if 'Click_Time' in df.columns:
                click_count = df['Click_Time'].notna().sum()
            else:
                logger.warning("'Click_Time' column not found in performance data")

            click_rate = (click_count / total_emails) * 100 if total_emails > 0 else 0
            click_to_open_rate = (click_count / open_count) * 100 if open_count > 0 else 0

            unsub_count = 0
            if 'Unsub_Time' in df.columns:
                unsub_count = df['Unsub_Time'].notna().sum()
            else:
                logger.warning("'Unsub_Time' column not found in performance data")

            unsub_rate = (unsub_count / total_emails) * 100 if total_emails > 0 else 0

            # Prepare visualization data
            visualization_data = []
            try:
                if 'Send_Time' in df.columns:
                    df['Send_Time'] = pd.to_datetime(df['Send_Time'])
                    df['date'] = df['Send_Time'].dt.date

                    # Group by date and calculate daily metrics
                    viz_data = df.groupby('date').agg({
                        'user_email': 'count',
                        'Open_Time': lambda x: x.notna().sum(),
                        'Click_Time': lambda x: x.notna().sum()
                    }).reset_index()

                    viz_data.columns = ['date', 'total_sent', 'total_opened', 'total_clicked']

                    # Calculate rates
                    viz_data['open_rate'] = (viz_data['total_opened'] / viz_data['total_sent']) * 100
                    viz_data['click_rate'] = (viz_data['total_clicked'] / viz_data['total_sent']) * 100

                    # Convert date to string for serialization
                    viz_data['date'] = viz_data['date'].astype(str)

                    # Convert to dict for returning
                    visualization_data = viz_data.to_dict('records')
            except Exception as e:
                logger.error(f"Error preparing visualization data: {str(e)}")

            return {
                "total_emails": total_emails,
                "open_count": open_count,
                "open_rate": open_rate,
                "click_count": click_count,
                "click_rate": click_rate,
                "click_to_open_rate": click_to_open_rate,
                "unsub_count": unsub_count,
                "unsub_rate": unsub_rate,
                "visualization_data": visualization_data
            }

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {str(e)}")
            return {
                "total_emails": 0,
                "open_count": 0,
                "open_rate": 0,
                "click_count": 0,
                "click_rate": 0,
                "click_to_open_rate": 0,
                "unsub_count": 0,
                "unsub_rate": 0,
                "error": str(e)
            }

    def analyze_template_performance(self, performance_data):
        """
        Analyze template performance from the combined performance data.

        Args:
            performance_data (pd.DataFrame): DataFrame containing email performance data

        Returns:
            pd.DataFrame: DataFrame with template performance metrics
        """
        if performance_data.empty:
            logger.warning("No performance data provided for template analysis")
            return pd.DataFrame()

        try:
            # Group by template and calculate metrics
            template_metrics = performance_data.groupby('template_name').agg({
                'user_email': 'count',
                'Open_Time': lambda x: x.notna().sum(),
                'Click_Time': lambda x: x.notna().sum(),
                'Unsub_Time': lambda x: x.notna().sum()
            }).reset_index()

            template_metrics.columns = ['template_name', 'total_sent', 'total_opened', 'total_clicked', 'total_unsubscribed']

            # Calculate rates
            template_metrics['open_rate'] = (template_metrics['total_opened'] / template_metrics['total_sent']) * 100
            template_metrics['click_rate'] = (template_metrics['total_clicked'] / template_metrics['total_sent']) * 100
            template_metrics['unsub_rate'] = (template_metrics['total_unsubscribed'] / template_metrics['total_sent']) * 100
            template_metrics['click_to_open_rate'] = np.where(
                template_metrics['total_opened'] > 0,
                (template_metrics['total_clicked'] / template_metrics['total_opened']) * 100,
                0
            )

            return template_metrics

        except Exception as e:
            logger.error(f"Error analyzing template performance: {str(e)}")
            return pd.DataFrame()


# API Functions for external use

def predict_campaign_performance(campaign_file: str = None, performance_file: str = None) -> Dict[str, Any]:
    """
    Predict performance metrics for a campaign using trained models.

    Args:
        campaign_file: Path to campaign data CSV file
        performance_file: Path to historical performance data for training

    Returns:
        Dictionary with predicted performance metrics
    """
    predictor = PerformancePredictor()

    # Train models if performance file provided
    if performance_file:
        predictor.train_models(performance_file)

    return predictor.predict_campaign_performance(campaign_file)


def analyze_campaign_performance(performance_file: str = None) -> Dict[str, Any]:
    """
    Analyze campaign performance and calculate comprehensive metrics.

    Args:
        performance_file: Path to performance data CSV file

    Returns:
        Dictionary with performance metrics and visualization data
    """
    analyzer = PerformanceAnalyzer()
    return analyzer.calculate_performance_metrics(performance_file)


def get_template_performance_analysis(performance_data: pd.DataFrame) -> pd.DataFrame:
    """
    Analyze template-specific performance metrics.

    Args:
        performance_data: DataFrame containing performance data

    Returns:
        DataFrame with template performance analysis
    """
    analyzer = PerformanceAnalyzer()
    return analyzer.analyze_template_performance(performance_data)


def train_prediction_models(performance_file: str) -> bool:
    """
    Train prediction models using historical performance data.

    Args:
        performance_file: Path to historical performance data CSV

    Returns:
        True if training was successful, False otherwise
    """
    predictor = PerformancePredictor()
    return predictor.train_models(performance_file)


def get_performance_insights(performance_file: str = None, campaign_file: str = None) -> Dict[str, Any]:
    """
    Get comprehensive performance insights including analysis and predictions.

    Args:
        performance_file: Path to performance data CSV file
        campaign_file: Path to campaign data CSV file for predictions

    Returns:
        Dictionary with performance analysis and predictions
    """
    results = {}

    # Get performance analysis
    analyzer = PerformanceAnalyzer()
    results['analysis'] = analyzer.calculate_performance_metrics(performance_file)

    # Get performance predictions if campaign file provided
    if campaign_file:
        predictor = PerformancePredictor()
        if performance_file:
            predictor.train_models(performance_file)
        results['predictions'] = predictor.predict_campaign_performance(campaign_file)

    return results


if __name__ == "__main__":
    # Example usage

    # Analyze current performance
    performance_metrics = analyze_campaign_performance()
    print("Performance Analysis Results:")
    print(f"Total Emails: {performance_metrics['total_emails']}")
    print(f"Open Rate: {performance_metrics['open_rate']:.2f}%")
    print(f"Click Rate: {performance_metrics['click_rate']:.2f}%")
    print(f"Unsubscribe Rate: {performance_metrics['unsub_rate']:.2f}%")

    # Example prediction (uncomment to test with actual files)
    # predictions = predict_campaign_performance(
    #     campaign_file="data/campaign_results/latest_campaign.csv",
    #     performance_file="data/mail_performance/combined/all_performance_2024_01.csv"
    # )
    #
    # if predictions.get('success'):
    #     print(f"\nPredicted Performance:")
    #     print(f"Open Rate: {predictions.get('open_rate', 0):.2f}%")
    #     print(f"Click Rate: {predictions.get('click_rate', 0):.2f}%")
    #     print(f"Unsubscribe Rate: {predictions.get('unsub_rate', 0):.2f}%")
    # else:
    #     print(f"Prediction failed: {predictions.get('error', 'Unknown error')}")

    # Example comprehensive insights
    # insights = get_performance_insights(
    #     performance_file="data/mail_performance/combined/all_performance_2024_01.csv",
    #     campaign_file="data/campaign_results/latest_campaign.csv"
    # )
    #
    # print(f"\nComprehensive Insights:")
    # print(f"Analysis: {insights.get('analysis', {})}")
    # print(f"Predictions: {insights.get('predictions', {})}")
